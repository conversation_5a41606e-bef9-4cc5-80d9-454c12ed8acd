import { clsx } from 'clsx'
import type { GetServerSideProps } from 'next'
import Link from 'next/link'
import type { FC } from 'react'
import React, { useCallback } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { ContentWrapper } from '~/src/components-news/ContentWrapper/ContentWrapper.component'
import { FeaturedArticle } from '~/src/components-news/Teasers/FeaturedArticle'
import { FeaturedNext } from '~/src/components-news/Teasers/FeaturedNext'
import { LatestItem } from '~/src/components-news/Teasers/LatestItem'
import * as Teaser from '~/src/components-news/Teasers/Teasers'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { ArticleMoreButtonNewsPages } from '~/src/components/article-more-button/article-more-button.component'
import NewsMeta from '~/src/components/news/meta'
import { categoryOffset } from '~/src/features/news-landing-page/news-landing-page.util'
import { VideosSection } from '~/src/features/news-landing-page/section-videos.component'
import type {
  ArticleTeaserFragmentFragment,
  CommentaryTeaserFragmentFragment,
  NewsByCategoryGenericQuery,
  NewsIndexPageQueryQuery,
  NewsOtwListQuery,
} from '~/src/generated'
import {
  type NewsCategoriesValues,
  newsCategories,
} from '~/src/lib/news-categories.lib'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import fontStyles from '~/src/styles/news-typefaces.module.scss'
import { ssrQueries } from '~/src/utils/ssr-wrappers'

export const getServerSideProps = (async ({ res }) => {
  const { dehydratedState } = await ssrQueries({
    ctxRes: res,
    queries: [
      news.newsLandingPage({
        variables: {
          limit: 25,
          offset: 0,
        },
      }),
      news.newsTrending({
        variables: { limit: 10 },
      }),
      news.newsCommentaries({
        variables: { limit: 4, offset: 0 },
      }),
      news.newsCategoriesTree(),
    ],
  })

  return {
    props: {
      dehydratedState,
    },
  }
}) satisfies GetServerSideProps

const News: FC = () => {
  const { data } = kitcoQuery(
    news.newsLandingPage({
      variables: {
        limit: 25,
        offset: 0,
      },
      options: {
        select: React.useCallback((d: NewsIndexPageQueryQuery) => {
          const short = d?.queue
          return {
            queue: {
              ...short,
              items: short?.items?.filter((x: any) => {
                if (x?.featured === undefined) {
                  return x
                }
                if (x?.featured === true) {
                  return x
                }
              }),
            },
          }
        }, []),
      },
    }),
  )

  const topSection = data?.queue?.items?.slice(
    1,
    4,
  ) as ArticleTeaserFragmentFragment[]
  const latestNewsSidebar = data?.queue?.items?.slice(
    4,
    9,
  ) as ArticleTeaserFragmentFragment[]
  const secondSection = data?.queue?.items?.slice(
    9,
    13,
  ) as ArticleTeaserFragmentFragment[]

  const ignoredArticles = topSection?.concat(latestNewsSidebar, secondSection)

  return (
    <LayoutNewsLanding title="Latest News, Video News, Analysis and Opinions | KITCO NEWS">
      <NewsMeta />
      <div className="block max-w-full">
        {/* top section */}
        <ContentWrapper
          className={clsx(
            'block !max-w-full',
            'lg:grid lg:grid-cols-[1fr_320px]',
            '!px-0 lg:!px-10 xl:!px-0',
          )}
        >
          <div className={clsx('mr-0 lg:mr-5', '!px-5 md:!px-10 lg:!px-0')}>
            <FeaturedArticle
              data={data?.queue?.items?.[0] as ArticleTeaserFragmentFragment}
            />

            {/* articles underneath featured */}
            <div className="grid gap-[30px] md:grid-cols-3 md:gap-5">
              {topSection?.map((data) => (
                <FeaturedNext key={data.id} data={data} />
              ))}
            </div>
            {/* TODO: fix responsiveness, this fixed width one breaks fluidity */}
            <AdvertisingSlot
              id={'banner-1'}
              className={
                'mx-auto mt-10 h-[280px] w-[336px] tablet:h-[90px] tablet:w-[728px] no-print'
              }
            />
          </div>

          {/* latest news */}
          <SidebarContainer classNames={'!px-5 md:!px-10 lg:!px-0'}>
            <SidebarLeftBorder>
              <SidebarTitle>Latest News</SidebarTitle>
              <div className="flex h-full flex-col gap-[27px]">
                {latestNewsSidebar?.map(
                  (data: ArticleTeaserFragmentFragment) => (
                    <LatestItem key={data.id} data={data} />
                  ),
                )}
              </div>
            </SidebarLeftBorder>
          </SidebarContainer>
        </ContentWrapper>
        {/* end top section */}

        <HR className="lg:opacity-1 my-[10px] opacity-0 md:my-10" />

        {/* second row three columns */}
        <ContentWrapper
          className={clsx(
            'grid',
            'grid-cols-1 md:grid-cols-2 lg:grid-cols-[1fr_1fr_320px]',
            'pb-10 md:divide-x md:divide-ktc-borders',
            '!px-5 md:!px-10 xl:!px-0',
          )}
        >
          <Teaser.CtxProvider
            node={secondSection?.[0]}
            className="pb-[30px] pr-0 md:pb-0 md:pr-[30px]"
          >
            <Teaser.TImage
              height={508}
              width={808}
              className="max-w-full md:max-w-[400px]"
            />
            <Teaser.Category className="mt-2" />
            <Teaser.TitleLink className="my-2 text-[20px] leading-[130%]" />
            <Teaser.Summary className="mb-2 line-clamp-2 md:line-clamp-3" />
            <Teaser.DateTime />
          </Teaser.CtxProvider>
          <div className="flex flex-col gap-[14px]">
            {secondSection?.slice(1, 4)?.map((x) => (
              <Teaser.CtxProvider
                key={x.id}
                node={x}
                className="flex px-0 md:px-[30px]"
              >
                <Teaser.TImage
                  width={240}
                  height={180}
                  className="w-[120px] min-w-[120px]"
                  aspectRatio="aspect-[4/3]"
                />
                <div className="pl-4">
                  <Teaser.Category className="mb-[2px]" />
                  <div className="flex flex-col">
                    <Teaser.TitleLink className="mb-1 line-clamp-3 text-[16px] leading-[130%]" />
                    <Teaser.DateTime />
                  </div>
                </div>
              </Teaser.CtxProvider>
            ))}
          </div>
          <div className="hidden w-full justify-between pl-5 lg:block">
            <AdvertisingSlot
              id={'right-rail-1'}
              className={'mx-auto h-[250px] w-[300px] no-print'}
            />
          </div>
        </ContentWrapper>
        {/* end second row three columns */}

        <VideosSection />

        <AdvertisingSlot
          id={'banner-3'}
          className={
            'mx-auto mt-10 h-[280px] w-[336px] tablet:h-[90px] tablet:w-[728px] no-print'
          }
        />

        {/* trending and opinions */}
        <ContentWrapper
          className={clsx(
            'grid pt-10 lg:grid-cols-[1fr_320px]',
            '!px-0 lg:!px-10 xl:!px-0',
            // "!px-0 lg:px-6",
          )}
        >
          <TrendingNowSection />
          <OpinionsSection />
        </ContentWrapper>
        {/* end trending and opinions */}

        <HR className="my-10 hidden lg:block" />

        {/* categories */}
        <ContentWrapper
          className={clsx(
            'block !px-5 pt-10 md:!px-10 lg:grid lg:grid-cols-[1fr_320px] xl:!px-0',
          )}
        >
          <div className="grid gap-5 pr-0 md:grid-cols-3 lg:pr-[34px]">
            <CategorySection
              title="Commodities"
              urlAlias={newsCategories.commodities}
              offset={categoryOffset(
                ignoredArticles,
                newsCategories.commodities,
              )}
            />
            <CategorySection
              title="Cryptocurrencies"
              urlAlias={newsCategories.cryptocurrencies}
              offset={categoryOffset(
                ignoredArticles,
                newsCategories.cryptocurrencies,
              )}
            />
            <CategorySection
              title="Mining"
              urlAlias={newsCategories.mining}
              offset={categoryOffset(ignoredArticles, newsCategories.mining)}
            />
            <CategorySection
              title="Economy"
              urlAlias={newsCategories.economy}
              offset={categoryOffset(ignoredArticles, newsCategories.economy)}
            />
            {/* <CategorySection
              title="Conferences"
              urlAlias={newsCategories.conferences}
              offset={categoryOffset(
                ignoredArticles,
                newsCategories.conferences,
              )}
            /> */}
            <OTWCategorySection title="Off The Wire" />
          </div>
        </ContentWrapper>
      </div>
      <AdvertisingSlot
        id={'footer'}
        className="after:font-['Font Awesome 5 Pro]' fixed bottom-0 left-1/2
                  z-20
                  w-[320px]
                  -translate-x-1/2 after:absolute after:right-0 after:top-[-25px] after:w-[20px] after:cursor-pointer after:rounded after:bg-[#373737] after:text-center after:text-lg after:font-bold after:leading-[1.1] after:text-white after:content-['X'] tablet:h-[90px] tablet:w-[728px] desktop:hidden no-print"
      />
      {/* end categories */}
    </LayoutNewsLanding>
  )
}

export default News

const SidebarTitle: FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <h2
      className={clsx(
        fontStyles.titles,
        'mb-[27px] pb-2 text-[21px] text-xl font-bold uppercase',
        'border-b border-ktc-borders',
      )}
    >
      <span>{children}</span>
    </h2>
  )
}

const HR: FC<{ className?: string }> = ({ className }) => (
  <hr
    className={clsx(
      'h1 bg-ktc-borders',
      'mx-auto w-full xl:w-[1240px]',
      className,
    )}
  />
)

const SidebarLeftBorder: FC<{ children: React.ReactNode }> = ({ children }) => (
  <div
    className={clsx(
      'border-l-0 pl-0',
      'lg:border-l lg:border-ktc-borders lg:pl-[30px]',
    )}
  >
    {children}
  </div>
)

const SidebarContainer: FC<{
  children: React.ReactNode
  classNames?: string
}> = ({ children, classNames }) => (
  <div
    className={clsx(
      'w-full',
      'bg-[#F8F8F8] lg:bg-white',
      'grid gap-2.5 md:grid-cols-2 lg:block',
      'mt-10 px-5 py-10 lg:mt-0 lg:p-0',
      classNames,
    )}
  >
    {children}
  </div>
)

export const TrendingNowSection: FC = () => {
  const { data } = kitcoQuery(news.newsTrending({ variables: { limit: 10 } }))

  return (
    <div className="flex w-full flex-col !px-5 md:!px-10 lg:!px-0 lg:!pr-[30px]">
      <h2 className={clsx('pb-6 text-[24px] md:text-[32px]')}>
        <span>Now Trending</span>
      </h2>
      <div className="flex flex-col gap-[1.875rem]">
        {data?.nodeListTrending
          ?.slice(0, 5)
          .map((x: ArticleTeaserFragmentFragment, idx: number) => {
            if (idx === 0) {
              return (
                <Teaser.CtxProvider key={x.id} node={x} className="flex">
                  <Teaser.TImage
                    width={600}
                    height={340}
                    className={clsx(
                      'hidden md:block md:w-[325px] md:min-w-[325px]',
                    )}
                  />
                  <div className="flex flex-col pl-0 md:pl-5">
                    <Teaser.Category className="mb-1" />
                    <div className="flex flex-col">
                      <Teaser.TitleLink
                        className={clsx(
                          'mb-2 line-clamp-3 text-[20px] leading-[130%] md:line-clamp-2 md:text-[24px]',
                        )}
                      />
                      <Teaser.Summary className="mb-2 line-clamp-2 hidden md:line-clamp-3 md:block" />
                      <Teaser.DateTime className="justify-self-end" />
                    </div>
                  </div>
                </Teaser.CtxProvider>
              )
            }
            return (
              <Teaser.CtxProvider key={x.id} node={x} className="flex">
                <Teaser.TImage
                  width={304}
                  height={170}
                  className={clsx(
                    'relative hidden md:block md:w-[152px] md:min-w-[152px]',
                  )}
                />
                <div className="flex flex-col pl-0 md:pl-5">
                  <Teaser.Category className="mb-1" />
                  <Teaser.TitleLink className="mb-2 line-clamp-3 text-[20px] leading-[130%] md:mb-0" />
                  <Teaser.DateTime className="justify-self-end" />
                </div>
              </Teaser.CtxProvider>
            )
          })}
      </div>
    </div>
  )
}

export const OpinionsSection: FC = () => {
  const { data } = kitcoQuery(
    news.newsCommentaries({
      variables: { limit: 4, offset: 0 },
    }),
  )
  return (
    <SidebarContainer classNames={'!px-5 md:!px-10 lg:!px-0'}>
      <SidebarLeftBorder>
        <SidebarTitle>Opinion</SidebarTitle>
        <div className="flex flex-col gap-3">
          {data?.commentaries?.items?.map(
            (x: CommentaryTeaserFragmentFragment) => (
              <Teaser.CtxProvider
                node={x}
                key={x.id}
                className="border-b border-ktc-borders pb-5 last:border-0"
              >
                <Teaser.TitleLink className="mb-2 text-[16px] leading-[130%]" />
                <Teaser.AuthorDates />
              </Teaser.CtxProvider>
            ),
          )}
        </div>
        <ArticleMoreButtonNewsPages label="More Opinions" href="/opinion" />
      </SidebarLeftBorder>
    </SidebarContainer>
  )
}

export const CategorySection: FC<{
  title: string
  urlAlias: NewsCategoriesValues
  offset?: number
}> = ({ title, urlAlias, offset = 0 }) => {
  const { data } = kitcoQuery(
    news.newsByCategoryGeneric({
      variables: {
        limit: 3,
        offset: offset,
        urlAlias,
        includeRelatedCategories: false,
        includeEntityQueues: false,
      },
      options: {
        enabled: true,
        select: React.useCallback((d: NewsByCategoryGenericQuery) => {
          const short = d?.nodeListByCategory
          return {
            nodeListByCategory: {
              ...short,
              items: short?.items?.filter(
                (x) => x?.__typename !== 'Commentary',
              ),
            },
          }
        }, []),
      },
    }),
  )

  return (
    <div className="pb-10">
      <HeaderSectionCategory title={title} urlAlias={urlAlias} />
      <div className="divide-y divide-ktc-borders">
        {data?.nodeListByCategory?.items?.map(
          (x: ArticleTeaserFragmentFragment, idx: number) => (
            <div className="py-[18px] last:pb-0 md:relative" key={x.id}>
              {idx === 0 ? (
                <Teaser.CtxProvider node={x} className="flex flex-col">
                  <Teaser.TImage width={304} height={170} />
                  <Teaser.TitleLink className="mt-3 text-[20px] leading-[130%] md:min-h-[44px] md:text-[17px]" />
                  <Teaser.Summary className="summary my-2 md:min-h-[60px]" />
                  <Teaser.DateTime />
                </Teaser.CtxProvider>
              ) : (
                <Teaser.CtxProvider node={x} className="flex flex-col gap-2">
                  <Teaser.TitleLink className="text-[20px] leading-[130%] md:min-h-[44px] md:text-[17px]" />
                  <Teaser.DateTime />
                </Teaser.CtxProvider>
              )}
            </div>
          ),
        )}
      </div>
      <div className="block md:hidden">
        <ArticleMoreButtonNewsPages label="See More" href={urlAlias} />
      </div>
    </div>
  )
}

const HeaderSectionCategory = ({ title, urlAlias }) => {
  return (
    <h2>
      <Link href={urlAlias}>
        <span className="mt-[-5px] inline-block text-[21px] text-xl font-bold uppercase leading-[27px] text-[#373737] hover:underline">
          {title}
        </span>
      </Link>
    </h2>
  )
}

const OTWCategorySection: FC<{
  title: string
  offset?: number
}> = ({ title, offset = 0 }) => {
  const { data } = kitcoQuery(
    news.newsOTWList({
      variables: {
        limit: 3,
        offset: offset,
      },
      options: {
        enabled: true,
        select: useCallback((d: NewsOtwListQuery) => {
          const filteredArr =
            d?.nodeList?.items?.filter(
              (obj) => obj && Object.keys(obj).length !== 0,
            ) || []
          return {
            ...d,
            nodeListByCategory: {
              total: d?.nodeList?.total || 0,
              items: [...filteredArr],
            },
          }
        }, []),
      },
    }),
  )

  return (
    <div className="pb-10">
      <HeaderSectionCategory title={title} urlAlias="/news/off-the-wire" />
      <div className="divide-y divide-ktc-borders">
        {data?.nodeList?.items?.slice(0, 3).map((x: any, idx: number) => (
          <div className="py-[18px] last:pb-0 md:relative" key={x.id}>
            {idx === 0 ? (
              <Teaser.CtxProvider node={x} className="flex flex-col">
                <Teaser.TImage width={304} height={170} />
                <Teaser.TitleLink className="mt-3 text-[20px] leading-[130%] md:min-h-[44px] md:text-[17px]" />
                <Teaser.Summary className="summary my-2 md:min-h-[60px]" />
                <Teaser.DateTime />
              </Teaser.CtxProvider>
            ) : (
              <Teaser.CtxProvider node={x} className="flex flex-col gap-2">
                <Teaser.TitleLink className="text-[20px] leading-[130%] md:min-h-[44px] md:text-[17px]" />
                <Teaser.DateTime />
              </Teaser.CtxProvider>
            )}
          </div>
        ))}
      </div>
      <div className="block md:hidden">
        <ArticleMoreButtonNewsPages
          label="See More"
          href="/news/off-the-wire"
        />
      </div>
    </div>
  )
}
