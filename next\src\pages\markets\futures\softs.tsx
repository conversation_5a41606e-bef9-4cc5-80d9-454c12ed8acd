import type { FC } from 'react'
import BarchartChartGrid from '~/src/components-markets/BarchartChartGrid/BarchartChartGrid'
import useFuturesSofts from '~/src/components-markets/FuturesCell/softsDataCell'
import QuotesTable from '~/src/components/QuotesTable/QuotesTable'
import SkeletonChart from '~/src/components/SkeletonChart/SkeletonChart'
import FuturesCategoryPageWrapper from '~/src/components/futures-category-page-wrapper/futures-category-page-wrapper.component'
import { Barcharts } from '~/src/features/bar-charts/barcharts'

// export async function getServerSideProps() {
//   const apollo = initializeApollo()
//
//   await apollo.query({
//     query: componentData.query,
//     variables: componentData.variables,
//   })
//
//   return addApolloState(apollo, {
//     props: {},
//   })
// }

const FuturesSofts: FC = () => {
  const [softs, isLoading] = useFuturesSofts()

  return (
    <FuturesCategoryPageWrapper category="Softs">
      <>
        {softs.length > 0 ? (
          <BarchartChartGrid columns={1}>
            <Barcharts
              symbol={softs[0].symbol}
              title={softs[0].name}
              href={`/markets/futures/${softs[0].symbol}`}
            />
          </BarchartChartGrid>
        ) : (
          <SkeletonChart columns={1} />
        )}
        <div className="mb-8">
          <QuotesTable
            title={'Softs'}
            section={'futures'}
            data={softs}
            isLoading={isLoading}
          />
        </div>
      </>
    </FuturesCategoryPageWrapper>
  )
}

export default FuturesSofts
