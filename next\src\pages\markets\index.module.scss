.container {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 40px;
  margin-top: 20px;
  padding: 20px;

  @media screen and (max-width: 600px) {
    grid-template-columns: 1fr;
  }
}

.main {
}

.sidebar {
}

.block {
  margin: 0 0 40px 0;

  &:last-child {
    margin-bottom: 0;
  }
}

.twoColumn {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chartGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;

  @media screen and (max-width: 600px) {
    grid-template-columns: repeat(1, 1fr);
  }
}
