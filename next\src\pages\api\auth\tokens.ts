import type { NextApiRequest, NextApiResponse } from 'next'
import { randomBytes } from 'node:crypto'

/**
 * Create a safe token
 *
 * @param req : NextApiRequest The request object
 * @param res : NextApiResponse The response object
 */
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  randomBytes(48, (err, buffer) => {
    if (err) {
      res.status(500).send({ error: 'Token generation failed' })
    } else {
      const token = buffer.toString('hex')
      res.status(200).send({ token })
    }
  })
}
