/* Hide by default */
.desktopLayout {
  display: block;
}
.tabletLayout,
.mobileLayout {
  display: none;
}

/* Display based on screen size */
@media only screen and (max-width: 768px) {
  .desktopLayout {
    display: none;
  }
  .tabletLayout {
    display: none;
  }
  .mobileLayout {
    display: block;
  }
}

@media only screen and (min-width: 769px) and (max-width: 1240px) {
  .desktopLayout {
    display: none;
  }
  .tabletLayout {
    display: block;
  }
  .mobileLayout {
    display: none;
  }
}
