const StrippedString = (value: string): string => {
  if (typeof window !== 'undefined' && window.document) {
    const divCustom = document.createElement('div')
    divCustom.innerHTML = value
    const paragraphs = divCustom.querySelectorAll('p')

    for (let i = 0; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i]
      const paragraphContent = paragraph.textContent.trim()

      if (paragraphContent && !paragraph.querySelector('style')) {
        return paragraphContent
      }
    }
  }

  return null
}

export default StrippedString

export const firstSentence = (value: string): string => {
  if (typeof window !== 'undefined' && window.document) {
    const firstLine = value?.split('&#10')[0].replace('<pre>', '')

    return firstLine
  }

  return null
}
