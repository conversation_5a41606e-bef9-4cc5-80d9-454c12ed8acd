import { LoggingBunyan } from '@google-cloud/logging-bunyan'
// @ts-ignore
import bunyan from 'bunyan'
import type { NextApiRequest, NextApiResponse } from 'next'

export default function logComponentErrorsToGCP(
  _req: NextApiRequest,
  res: NextApiResponse,
) {
  // Creates a Bunyan Cloud Logging client
  const loggingBunyan = new LoggingBunyan()

  // Create a Bunyan logger that streams to Cloud Logging
  // Logs will be written to: "projects/YOUR_PROJECT_ID/logs/bunyan_log"
  const logger = bunyan.createLogger({
    // The JSON payload of the log as it appears in Cloud Logging
    // will contain "name": "my-service"
    name: 'my-service',
    streams: [
      // Log to the console at 'info' and above
      { stream: process.stdout, level: 'info' },
      // And log to Cloud Logging, logging at 'info' and above
      loggingBunyan.stream('info'),
    ],
  })

  // Writes some log entries
  logger.error('warp nacelles offline')
  logger.info('shields at 99%')

  return res.status(200).json({ message: 'logged' })
}
