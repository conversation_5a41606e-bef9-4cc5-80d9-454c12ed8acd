import type { NextApiRequest, NextApiResponse } from 'next'
import type {
  ArticleTeaserFragmentFragment,
  Commentary,
  CommentaryTeaserFragmentFragment,
  NewsArticle,
  Sponsored,
} from '~/src/generated'

export type ArticlesUnion = NewsArticle // | Commentary
export type OpinionsUnion = Commentary

export type SponsoredUnion = Sponsored

export type TeasersUnion = ArticleTeaserFragmentFragment
export type TeasersCommentaryUnion = CommentaryTeaserFragmentFragment
// | Commentary

export type NextApiRoute = (
  req: NextApiRequest,
  res: NextApiResponse,
) => Promise<void>
