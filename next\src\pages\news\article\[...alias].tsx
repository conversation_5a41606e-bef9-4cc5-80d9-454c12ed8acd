import type { UseQueryResult } from '@tanstack/react-query'
import type { GetServerSideProps, NextPage } from 'next'
import { useRouter } from 'next/router'
import { useEffect, useMemo, useState } from 'react'
import { useInView } from 'react-intersection-observer'
import { ArticleContent } from '~/src/components-news/Article/ArticleContent'
import { RelatedArticles } from '~/src/components-news/RelatedNews/RelatedArticles'
import ArticlePageEmpty from '~/src/components/Article/ArticlePageEmpty'
import ArticleQueueContent from '~/src/components/Article/ArticleQueueContent'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { Query } from '~/src/components/Query/Query'
import NewsMeta from '~/src/components/news/meta'
import type { NewsArticle, NewsByCategoryGenericQuery } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import StrippedString from '~/src/utils/strippedString'
import { urlSafePath } from '~/src/utils/url-safe-path'
import styles from './article-alias.module.scss'

const ArticlePage: NextPage<{ urlAlias: string; auHash?: string }> = ({
  urlAlias,
  auHash,
}) => {
  // The supported types for the article queue
  const TYPE_NAME_SUPPORT = ['OffTheWire', 'NewsArticle'] as string[]

  // Limit for infinite scroll articles
  const limitInfinteScrollArticle = 4

  // Fetch the article data
  const { data } = kitcoQuery(
    news.nodeByUrlAlias({
      variables: { urlAlias, auHash },
      options: {
        enabled: true,
      },
    }),
  )

  // Get the article data
  const articleData = data?.nodeByUrlAlias as NewsArticle

  // TODO: Temporarily hide the conferences page
  useEffect(() => {
    if (articleData?.category?.urlAlias === '/news/category/conferences') {
      const router = useRouter()
      router.push('/news')
    }
  }, [articleData?.category?.urlAlias])

  // If the article data is not available, display an empty page
  if (!articleData) return <ArticlePageEmpty />

  // Get the first paragraph of the article
  const pFirst = StrippedString(
    articleData?.bodyWithEmbeddedMedia.value,
  )?.replace('&nbsp;', ' ')

  // Use the in-view hook for the infinite scroll
  const { ref } = useInView()

  // State to store the list of URL aliases in the queue
  const [listUrlAliasInQueue, setListUrlAliasInQueue] = useState([])

  // Fetch the related articles
  const fetcher = useMemo(() => {
    return news.newsByCategoryGeneric({
      variables: {
        limit: 6,
        offset: 0,
        includeRelatedCategories: false,
        includeEntityQueues: true,
        urlAlias: articleData.category?.urlAlias,
      },
      options: {
        enabled: true,
        select: (d: any) => {
          const filterItems = d?.nodeListByCategory?.items
            ?.filter(
              (x: any) =>
                Object.keys(x).length !== 0 && x.id !== articleData.id,
            )
            ?.map((x) => {
              return x.urlAlias
            })
            ?.slice(0, limitInfinteScrollArticle)
          setListUrlAliasInQueue(filterItems)
          return filterItems
        },
      },
    })
  }, [articleData.category?.urlAlias, articleData.id])

  return (
    <LayoutNewsLanding title={`${data?.nodeByUrlAlias?.title} | Kitco News`}>
      <NewsMeta
        title={data?.nodeByUrlAlias?.title}
        description={pFirst}
        image={
          articleData?.teaserImage?.detail?.default?.srcset ??
          articleData?.image?.detail?.default?.srcset
        }
        authorTwitter={articleData?.author?.twitterId}
        urlAlias={articleData?.urlAlias}
      />
      <div className={styles.parentWrapper}>
        <ArticleContent articleData={articleData} counter={-1} />
        <Query fetcher={fetcher}>
          {(articles: UseQueryResult<NewsByCategoryGenericQuery>) => {
            if (articles.error) {
              return (
                <p className="my-8 text-center">Oh no, something went wrong</p>
              )
            }
            return (
              <ArticleQueueContent
                articles={articles}
                TYPE_NAME_SUPPORT={TYPE_NAME_SUPPORT}
              />
            )
          }}
        </Query>
      </div>
      <div ref={ref} />
      <div className="mx-auto mt-10 w-full max-w-full px-5 md:flex md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <div className="md:w-[190px]" />
        <div className="md:w-[calc(100%_-_190px)] md:pl-10">
          <article>
            <div className="flex justify-items-end gap-10 lg:grid-cols-3">
              <div className="col-span-1 lg:col-span-2">
                <RelatedArticles
                  listUrlAliasInQueue={listUrlAliasInQueue}
                  currentNodeId={articleData?.id}
                  currentNodeCategory={articleData?.category?.urlAlias}
                />
              </div>
              <aside className="hidden min-w-[300px] lg:col-span-1 lg:block" />
            </div>
          </article>
        </div>
      </div>
    </LayoutNewsLanding>
  )
}
export default ArticlePage

export const getServerSideProps: GetServerSideProps = async (c) => {
  const slugs = c.query.alias as Array<string>
  const auHash = c?.query?.auHash ?? ('' as any)

  const fullSlug = `/news/article/${slugs
    ?.map((x) => urlSafePath(x))
    .join('/')}`

  const { dehydratedState } = await ssrQueries({
    ctxRes: c.res,
    queries: [
      news.nodeByUrlAlias({
        variables: {
          urlAlias: fullSlug,
          auHash,
        },
      }),
      news.newsCategoriesTree(),
      news.newsTrending({
        variables: { limit: 10 },
      }),
    ],
  })

  for (const x of dehydratedState.queries) {
    const queryKey = x.queryKey.find((item) => item?.urlAlias || false)
    if (queryKey?.urlAlias === fullSlug && !x.state?.data?.nodeByUrlAlias) {
      return { notFound: true }
    }
  }

  // cache control one hour
  c.res.setHeader('Cache-Control', 's-maxage=3600, stale-while-revalidate')

  return {
    props: {
      dehydratedState,
      urlAlias: fullSlug,
      auHash,
    },
  }
}
