import type { NextRequest } from 'next/server'

export const config = {
  runtime: 'edge',
}

export default async function (req: NextRequest) {
  const url = new URL(req.url)

  const statisticsResponse = await fetch(
    `https://cms.prod.kitco.com/core/modules/statistics/statistics.php`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      // body: new URLSearchParams(`nid=${req.query.nid}`),
      body: new URLSearchParams(`nid=${url.searchParams.get('nid')}`),
    },
  ).catch((err: Error) => err)

  if (statisticsResponse instanceof Error || !statisticsResponse.ok) {
    // @ts-ignore
    return Response.json({ status: 'Failed' })
  }

  // @ts-ignore
  return Response.json({ status: 'OK' })
}
