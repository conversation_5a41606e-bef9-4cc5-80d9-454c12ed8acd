import { useRouter } from 'next/router'
import LoginForm, { FormState } from '~/src/components/Auth/Form/LoginForm'
import LayoutLanding from '~/src/components/LayoutLanding/LayoutLanding'

export default function Index() {
  const router = useRouter()

  const handleOnSuccess = () => {
    // If we're in a popup, send a message to the parent window
    if (window.opener) {
      window.opener.postMessage('loginSuccess', '*')
      window.close() // Close the popup
    } else {
      router.push('/') // Redirect to the home page if not in a popup
    }
  }

  return (
    <LayoutLanding title={'Login'}>
      <LoginForm defaultState={FormState.LOGIN} onSuccess={handleOnSuccess} />
    </LayoutLanding>
  )
}
