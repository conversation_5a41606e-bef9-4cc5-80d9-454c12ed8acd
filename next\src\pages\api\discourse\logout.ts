import type { NextApiRequest, NextApiResponse } from 'next'
import { revokeUserTokens } from '~/src/services/firebase/admin/service'

/**
 * The handler function for logout requests
 *
 * @param req : NextApiRequest The request object
 * @param res : NextApiResponse The response object
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  // Only allow POST requests
  if (req.method !== 'POST')
    return res.status(405).json({ error: 'Method Not Allowed' })

  // Validate the incoming request to ensure it's from Discourse
  if (!verifyRequestFromDiscourse(req)) {
    return res.status(403).json({ error: 'Request validation failed' })
  }

  if (req.headers['x-discourse-event'] === 'ping') {
    return res.status(200).json({ message: 'Pong' })
  }

  // Check if the required user information is present
  const { user } = req.body
  if (!user || !user.external_id) {
    return res.status(400).json({ error: 'Missing required data' })
  }

  try {
    const result = revokeUserTokens(user.external_id)

    if (result) {
      return res.status(200).json({ message: 'User logged out successfully' })
    }

    return res.status(500).json({ error: 'Failed to revoke user tokens' })
  } catch (error) {
    console.error('Error verifying Firebase ID token:', error)
    res.status(401).json({ error: 'Failed to authenticate user' })
  }
}

/**
 * Verify if the request came from Discourse
 */
export function verifyRequestFromDiscourse(req: NextApiRequest) {
  // Implement logic based on headers, IP, or signatures
  return (
    req.headers['x-discourse-event'] === 'user_logged_out' ||
    req.headers['x-discourse-event'] === 'ping'
  )
}
