import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import type { GetServerSideProps } from 'next'
import Link from 'next/link'
import type { FC, ReactNode } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import GlobalMeta from '~/src/components/GlobalMeta/GlobalMeta'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import type {
  MiningPressReleaseQueueQuery,
  PressRelease,
} from '~/src/generated'
import { mining } from '~/src/lib/mining.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import styles from '~/src/styles/pages/PressRelease.module.scss'
import cs from '~/src/utils/cs'
import { useInfinite, useParams } from '~/src/utils/infiniteScroll'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import useScreenSize from '~/src/utils/useScreenSize'

dayjs.extend(utc)
dayjs.extend(timezone)

interface ItemPropsType {
  id: string | number
  url: string
  title: string
  updatedAt: string
  inNodeQueue: boolean
}

export const getServerSideProps: GetServerSideProps = async (c) => {
  const { dehydratedState } = await ssrQueries({
    ctxRes: c.res,
    queries: [
      mining.nodeListPressReleaseQueue({ variables: { limit: 40, offset: 0 } }),
    ],
  })
  return {
    props: { dehydratedState },
  }
}

const PressReleases: FC = () => {
  const { params, incrementParams } = useParams(40, 40)

  const { data } = kitcoQuery(
    mining.nodeListPressReleaseQueue({
      variables: { ...params, queueId: 'press_releases' },
      options: { enabled: true },
    }),
  )

  return (
    <LayoutNewsLanding title="Mining Press Releases | KITCO Mining">
      <GlobalMeta
        title="Mining Press Releases | KITCO Mining"
        description="Latest Press Releases form the Mining Industry."
        keywords="KITCO Mining, Press Releases, Canadian Resource Stocks, Mining Stocks, Junior Mining Stocks, Mining Equipment, Mining Markets, Dividends, Uranium, Nuclear Energy, Mine Development, Mining Picks"
        type="press-releases"
      />
      <div className="mx-auto box-border w-full max-w-full px-[20px] md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <TitlePressRelease />
        <div className="lg:grid-cols-layout-1 grid gap-8 px-0 sm:grid-cols-1">
          <div>
            <hr className="mt-[5px] text-[#bfbfbf]" />
            <div className="flex gap-8 pb-[30px] pt-5">
              <DataTableReleases
                data={data}
                incrementParams={incrementParams}
              />
              <AdsOnDesktop />
            </div>
          </div>
        </div>
      </div>
    </LayoutNewsLanding>
  )
}

const TitlePressRelease: FC = () => {
  const baseH1 = 'uppercase text-[26px] md:text-[48px] text-ktc-date-gray'
  const baseSubH1 =
    'uppercase text-[26px] md:leading-[58px] leading-[38px] md:text-[48px]'

  const handleLink = '/news/category/mining'

  return (
    <>
      <h1 className="flex items-center justify-between">
        <div className="flex items-center leading-[38px] md:leading-[58px]">
          <Link href={handleLink}>
            <h1 className={cs([baseH1])}>{'Mining'}</h1>
          </Link>

          <h1 className={cs([baseH1, 'px-1 md:px-2'])}>/</h1>
          <h1 className={cs([baseSubH1, 'text-kitco-black'])}>
            latest mining press releases
          </h1>
        </div>
      </h1>
      <AdvertisingSlot
        id="press-release"
        className="my-4 flex min-h-[100px] w-[100%] min-w-[320px] items-center justify-center
                native-sm:min-h-[90px] native-sm:w-[728px] no-print"
      />
    </>
  )
}

const DataTableReleases: FC<{
  data: MiningPressReleaseQueueQuery
  incrementParams: () => void
}> = ({ data, incrementParams }) => {
  const pressReleaseItemsFromData = data?.queue?.items || []
  const pressReleaseItems = pressReleaseItemsFromData
    .filter((x: PressRelease) => typeof x.id !== 'undefined')
    .map((x: PressRelease) => ({
      ...x,
      inNodeQueue: data.ids.includes(x.id),
    }))
  const { items, fetchMore, isNextPage } = useInfinite({
    items: pressReleaseItems,
    incrementParams,
    total: data?.queue?.total,
  })

  const dateRelease = (date: string): string => {
    return dayjs(date)
      .tz(process.env.NEXT_PUBLIC_TIMEZONE)
      .format('MMM DD, YYYY h:mm A')
  }

  const ShowDataReleases = ({ items }): ReactNode => {
    if (!items) return null

    return items.map((x: ItemPropsType, idx: number) => {
      const cssClasses = x?.inNodeQueue
        ? 'text-sm !font-semibold text-gray-800'
        : 'text-sm !font-normal text-gray-800'
      return (
        <tr key={x.id} className="border border-gray-300">
          {idx === 8 ? (
            <td className="p-0">
              <AdvertisingSlot
                id="mining-content-billboard"
                className="mx-auto my-4 flex min-h-[600px] w-[100%] max-w-[300px] items-center justify-center border-[1px] border-[#ccc]
                    native-sm:my-0 native-sm:h-[252px] native-sm:min-h-[200px] native-sm:max-w-[970px] native-sm:border-0 no-print"
              />
            </td>
          ) : (
            <td className="grid grid-cols-none items-center justify-between px-4 py-1 md:grid-cols-[70%,auto]">
              <a
                href={x.url}
                className="w-full grow"
                target="_blank"
                rel="noreferrer"
              >
                <h5 className={cs([cssClasses, styles.title])}>{x.title}</h5>
              </a>
              <time
                dateTime={x.updatedAt}
                className="w-full whitespace-nowrap text-[0.8125rem] !font-normal text-[#757575] md:text-right"
              >
                {dateRelease(x.updatedAt)}
              </time>
            </td>
          )}
        </tr>
      )
    })
  }

  return (
    <section className="w-full">
      <table className="w-full border border-gray-300">
        <tbody>
          <ShowDataReleases items={items} />
        </tbody>
      </table>
      <AdvertisingSlot
        id={'banner-2'}
        className={
          'tablet:margin-left:-20px desktop:margin-left:0 mx-auto my-[70px] h-[280px] w-[336px] md:h-[90px] md:w-[728px] no-print'
        }
      />

      <LoadMoreButton isNextPage={isNextPage} fetchMore={fetchMore} />
    </section>
  )
}

const LoadMoreButton: FC<{ isNextPage: boolean; fetchMore: any }> = ({
  isNextPage,
  fetchMore,
}) => {
  return (
    <div className="text-center">
      <button
        className={cs([
          'rounded-md text-base',
          !isNextPage && 'cursor-not-allowed text-ktc-date-gray',
        ])}
        type="button"
        onClick={() => fetchMore()}
      >
        <span className="text-sm font-bold uppercase tracking-normal">
          load more press releases
        </span>
        <div className={styles.chevronDown}>
          <svg
            className={cs([styles.mdsIcon, !isNextPage && styles.iconDisable])}
            id="icon-arrow-down"
            viewBox="0 0 24 24"
          >
            <title>icon-arrow-down</title>
            <polyline points="20.48 7.76 12 16.24 3.52 7.76" />
          </svg>
          <svg
            className={cs([styles.mdsIcon, !isNextPage && styles.iconDisable])}
            id="icon-arrow-down"
            viewBox="0 0 24 24"
          >
            <title>icon-arrow-down</title>
            <polyline points="20.48 7.76 12 16.24 3.52 7.76" />
          </svg>
        </div>
      </button>
    </div>
  )
}

const AdsOnDesktop: FC = () => {
  const { isDesktop } = useScreenSize()

  if (!isDesktop) return null

  return (
    <div className="relative w-[300px]">
      <AdvertisingSlot
        id={'right-rail-1'}
        className={'sticky top-4 mx-auto h-[250px] w-[300px] no-print'}
      />
    </div>
  )
}

export default PressReleases
