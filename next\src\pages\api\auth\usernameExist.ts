import type { NextApiRequest, NextApiResponse } from 'next'
import { sanitizeUsername } from '~/src/features/auth/sanitize'
import { checkUsernameExists } from '~/src/services/firebase/admin/database'

/**
 * Checks if the username exists in the database
 *
 * @param req : NextApiRequest The request object
 * @param res : NextApiResponse The response object
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  const { username } = req.body
  try {
    if (!username) {
      return res.status(400).json({ error: 'Username is required' })
    }

    const { original, lowercase } = sanitizeUsername(username)

    const usernameExists = await checkUsernameExists(lowercase)

    res.status(200).json({
      username: username,
      displayUsername: original,
      exists: usernameExists,
    })
  } catch (error) {
    console.error('Server error:', error)
    res.status(500).json({ message: 'Server error' })
  }
}
