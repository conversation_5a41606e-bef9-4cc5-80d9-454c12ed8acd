import { Storage } from '@google-cloud/storage'
import * as path from 'node:path'

// Load the Google Cloud Storage key file.
const keyFilename = path.join(
  process.cwd(),
  'secrets',
  'google-cloud-storage.json',
)

// Create a new Storage instance.
const storage = new Storage({ keyFilename })

/**
 * Save a file to Google Cloud Storage
 *
 * @param data
 * @param fileName
 * @param contentType
 * @param gzip
 * @param bucketName
 */
export async function saveFileToGCS(
  data: string,
  fileName: string,
  contentType: string,
  gzip: boolean,
  bucketName = process.env.NEXT_PUBLIC_BUCKET,
) {
  const bucket = storage.bucket(bucketName)
  const file = bucket.file(fileName)

  await file.save(data, {
    contentType: contentType,
    gzip: gzip,
  })
}
