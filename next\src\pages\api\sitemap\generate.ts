import type { NextApiRequest, NextApiResponse } from 'next'
import { StoreSitemap } from '~/src/features/sitemaps/utils'

/**
 * Generate and store the sitemaps.
 *
 * @param req
 * @param res
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method !== 'POST') return res.status(401).end()

  // Start the sitemap generation process
  generateSitemaps()

  res.status(202).send({ message: 'Sitemap generation in progress.' })
}

/**
 * Generate and upload sitemaps for the website
 */
async function generateSitemaps() {
  try {
    await StoreSitemap('getAllNewsArticles', 'news.xml')
    await StoreSitemap('getAllOffTheWire', 'off-the-wire.xml')
    await StoreSitemap('getAllOpinions', 'opinions.xml')
    await StoreSitemap('video', 'video.xml')

    console.log('Sitemaps generated and uploaded successfully.')
  } catch (error) {
    console.error('Failed to generate or upload sitemaps:', error)
  }
}
