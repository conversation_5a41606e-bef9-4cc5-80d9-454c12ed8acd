import LoginForm from '~/src/components/Auth/Form/LoginForm'
import { Alert } from '~/src/components/Auth/Messages/Alert'
import AlreadyLoggedIn from '~/src/components/Auth/Messages/AlreadyLoggedIn'
import LayoutLanding from '~/src/components/LayoutLanding/LayoutLanding'
import { useDiscourseAuth } from '~/src/hooks/Auth/useDiscourseAuth'
import { logout } from '~/src/services/firebase/service'

/**
 * The Connect component is responsible for handling the login process.
 * It checks if the user is logged in, fetches the SSO token from the server,
 * and redirects the user to Discourse.
 *
 * If the user is not logged in, it displays the login form.
 *
 * If an error occurs during the login process, it displays an error message.
 */
export default function Connect() {
  const {
    isLoggedIn,
    error,
    forumUrl,
    formState,
    handleRedirect,
    setIsRegistering,
    setNeedUsername,
    waitingForToken,
  } = useDiscourseAuth()

  return (
    <LayoutLanding title={'Login'}>
      {error && (
        <div className="container mx-auto px-4 py-8 text-center">
          <Alert message={error} />
        </div>
      )}

      {isLoggedIn ? (
        error ? (
          <div className="items-top flex h-screen items-center justify-center py-10">
            <div className="text-center">
              <h2 className="text-2xl font-semibold">
                Oops, something went wrong
              </h2>
              <p className="mt-2 text-lg">
                An error occurred while trying to log in. Please try again.
              </p>
              {forumUrl && (
                <button
                  type="button"
                  className="mt-4 text-ktc-blue underline hover:text-ktc-black"
                  onClick={handleRedirect}
                >
                  Back to the forum
                </button>
              )}
            </div>
          </div>
        ) : (
          <AlreadyLoggedIn
            onClose={handleRedirect}
            onLogout={() => logout()}
            buttonText="Continue to the forum"
            loadingText="Connecting to the forum..."
            processing={waitingForToken}
            disableRedirectButton={waitingForToken}
          />
        )
      ) : (
        <div className="flex flex-row">
          <div className="flex-1">
            <LoginForm
              logo="/kitco_forum_logo.png"
              link={process.env.NEXT_PUBLIC_DISCOURSE_URL}
              defaultState={formState}
              onIsRegistering={(value) => setIsRegistering(value)}
              onSuccess={() => setNeedUsername(false)}
              redirectToForum={true}
              showRegister={true}
              handleAuthStatus={false}
            />
          </div>
        </div>
      )}
    </LayoutLanding>
  )
}
