/**
 * This function removes newlines from paragraphs with images.
 *
 * @param html
 */
export const handleImages = (html: string) => {
  // Regular expression to find <p> elements within #articleBody
  const articleBodyRegex = /<div id="articleBody">([\s\S]*?)<\/div>/i
  const paragraphRegex = /<p[^>]*>([\s\S]*?)<\/p>/gi
  const imgTagRegex = /<img[^>]*>/i

  // Extract the content of #articleBody
  const articleBodyMatch = html.match(articleBodyRegex)
  if (!articleBodyMatch) {
    return html // Return original HTML if no #articleBody is found
  }

  let newHtml = articleBodyMatch[1]

  // Find and process paragraphs with images
  newHtml = newHtml.replace(paragraphRegex, (paragraph) => {
    if (imgTagRegex.test(paragraph)) {
      return paragraph.replace(/\n/g, '')
    }
    return paragraph
  })

  return newHtml
}
