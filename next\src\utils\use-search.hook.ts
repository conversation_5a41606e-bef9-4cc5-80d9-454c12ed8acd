import { search } from 'fast-fuzzy'
import { useRouter } from 'next/router'
import { useCallback, useEffect, useRef, useState } from 'react'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { useParams } from '~/src/utils/infiniteScroll'
import { kitcoCryptos, kitcoMetals } from '~/src/utils/kitcoDatas'
import * as timestamps from '~/src/utils/timestamps'
import { news } from '../lib/news-factory.lib'
import { useReduce } from './use-reduce.hook'

const initialCriteria = {
  inputTerm: '',
  currency: null,
  symbol: null,
  timeStamp: timestamps.current(),
  sort: 'Relevance',
}

export type SearchCriteria = Omit<
  typeof initialCriteria,
  'currency' | 'symbol'
> & {
  currency: string | null
  symbol: string | null
}

const fuzzySearchForExtraneousData = (term: string) => {
  const metalOrCrypto = search(term, [...kitcoMetals, ...kitcoCryptos], {
    keySelector: (x) => x.name || x.symbol,
  })

  return {
    priceSymbolAndCurrency: metalOrCrypto?.[0] || null,
  }
}

export function useSearchStateAndEvents() {
  const router = useRouter()
  const { params, incrementParams, resetParams } = useParams(10)
  const [items, setItems] = useState([])
  const [criteria, setState] = useReduce<SearchCriteria>(initialCriteria)
  const { data, isFetching } = useSearchStrAsyncFetch(criteria, params)
  const { data: dataMetaCrypto } = useSearchAsyncFetch(criteria, params)
  const prevProp = useRef('')

  useEffect(() => {
    if (prevProp.current === criteria.inputTerm) return
    router.push(
      { pathname: '/search', query: { term: criteria.inputTerm } },
      undefined,
      {
        shallow: true,
      },
    )
  }, [criteria.inputTerm])

  useEffect(() => {
    if (data?.searchData) {
      if (prevProp.current === criteria.inputTerm) {
        setItems([...items, ...data.searchData.items])
      } else {
        setItems(data.searchData.items)
        prevProp.current = criteria.inputTerm
      }
    }
  }, [data, criteria.inputTerm])

  const fetchMore = () => {
    if (data?.searchData?.total > items.length) {
      incrementParams()
    }
  }

  const setResultsState = useCallback(
    (term: string) => {
      const results = fuzzySearchForExtraneousData(term)
      setState({
        inputTerm: term,
        symbol: results?.priceSymbolAndCurrency?.symbol ?? null,
        currency: results?.priceSymbolAndCurrency?.currency ?? null,
      })
    },
    [JSON.stringify(router)],
  )

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href)
      const term = url.searchParams.get('term')
      setResultsState(term ?? '')
    }
  }, [setResultsState])

  const resetSearchTerms = () => {
    setItems([])
    resetParams()
  }

  const onSearchButtonClick: React.FormEventHandler<HTMLFormElement> = (e) => {
    const form = new FormData(e.currentTarget)
    e.preventDefault()
    const term = form.get('search') as string
    if (criteria.inputTerm !== term) {
      resetSearchTerms()
      setResultsState(term)
    }
  }

  const onSortSelected = (value) => {
    resetSearchTerms()
    setState({ sort: value })
  }

  const onSearchClear = () => {
    setState(initialCriteria)
    router.push({ pathname: '/search' }, undefined, {
      shallow: true,
    })
  }

  return {
    state: {
      criteria,
      data: {
        ...dataMetaCrypto,
        searchData: { ...data?.searchData, items: items },
      },
      isFetching,
      // ref,
      params,
      fetchMore,
    },
    handlers: {
      onSearchButtonClick,
      onSearchClear,
      onSortSelected,
    },
  }
}

export function useSearchAsyncFetch(criteria: SearchCriteria, params) {
  return kitcoQuery(
    news.searchMetals({
      variables: {
        timestamp: criteria.timeStamp,
        currency: criteria.currency ?? '',
        symbol: criteria.symbol ?? '',
        ...params,
      },
      options: {
        enabled: true,
      },
    }),
  )
}

export function useSearchStrAsyncFetch(criteria: SearchCriteria, params) {
  return kitcoQuery(
    news.searchNews({
      variables: {
        query: criteria.inputTerm,
        sort: criteria.sort || 'Date', // Relevance | Date
        limit: 10,
        offset: 0,
        ...params,
      },
      options: {
        enabled: true,
      },
    }),
  )
}
