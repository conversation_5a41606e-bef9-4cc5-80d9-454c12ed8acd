import type { Dayjs } from 'dayjs'

/**
 * Interface for market status
 *
 * @property {boolean} marketOpen - Whether the market is open
 * @property {Dayjs} marketOpenTime - The time when the market opens (or last known open time if market is closed)
 * @property {Dayjs} marketCloseTime - The time when the market closes (or last known close time if market is closed)
 */
interface MarketStatus {
  chartDayEnd: Dayjs
  chartDayStart: Dayjs
  marketCloseTime: Dayjs
  marketOpen: boolean
  marketOpenTime: Dayjs
}

export default MarketStatus
