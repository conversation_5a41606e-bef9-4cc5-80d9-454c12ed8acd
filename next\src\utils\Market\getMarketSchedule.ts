/**
 * Get the market schedule from environment variables
 *
 * @returns {Object} An object containing the market schedule values
 */
function getMarketSchedule() {
  const { openHourNumber, closeHourNumber, openDayNumber, closeDayNumber } =
    parseFromEnv()

  // Default to 6 PM
  const openHour = Number.isNaN(openHourNumber) ? 18 : openHourNumber

  // Default to 5 PM
  const closeHour = Number.isNaN(closeHourNumber) ? 17 : closeHourNumber

  // Default to Sunday
  const openDay = (
    Number.isNaN(openDayNumber) || openDayNumber < 0 || openDayNumber > 6
      ? 0 // Default to Sunday if invalid
      : openDayNumber
  ) as 0 | 1 | 2 | 3 | 4 | 5 | 6

  // Default to Friday
  const closeDay = (
    Number.isNaN(closeDayNumber) || closeDayNumber < 0 || closeDayNumber > 6
      ? 5 // Default to Friday if invalid
      : closeDayNumber
  ) as 0 | 1 | 2 | 3 | 4 | 5 | 6

  // Create an array with the days the market is open, including openDay and closeDay
  const openDays = []
  for (let day: number = openDay; day !== closeDay; day = (day + 1) % 7) {
    openDays.push(day as 0 | 1 | 2 | 3 | 4 | 5 | 6)
  }

  return {
    closeDay,
    closeHour,
    openDay,
    openDays,
    openHour,
  }
}

/**
 * Parse the market schedule from environment variables
 *
 * @returns {Object} An object containing the market schedule values
 */
const parseFromEnv = () => {
  const openHourNumber = Number.parseInt(
    process.env.NEXT_PUBLIC_MARKETS_OPEN_HOUR,
  )

  const closeHourNumber = Number.parseInt(
    process.env.NEXT_PUBLIC_MARKETS_CLOSE_HOUR,
  )

  const openDayNumber = Number.parseInt(
    process.env.NEXT_PUBLIC_MARKETS_OPEN_DAY,
  )

  const closeDayNumber = Number.parseInt(
    process.env.NEXT_PUBLIC_MARKETS_CLOSE_DAY,
  )

  return { openHourNumber, closeHourNumber, openDayNumber, closeDayNumber }
}

export default getMarketSchedule
