import type { GetServerSideProps } from 'next'
import { AdvertisingSlot } from 'react-advertising'
import MarketPageIndicesCell from '~/src/components-markets/MarketPageIndicesCell/MarketPageIndicesCell'
import LatestNewsCell from '~/src/components-news/LatestNewsCell/LatestNewsCell'
import IndexDetailTitleBlock from '~/src/components/IndexDetailTitleBlock/IndexDetailTitleBlock'
import IndexHighLowTable from '~/src/components/IndexHighLowTable/IndexHighLowTable'
import Layout from '~/src/components/Layout/Layout'
import PageLayoutTwoColumns from '~/src/components/PageLayoutTwoColumns/PageLayoutTwoColumns'
import { Barcharts } from '~/src/features/bar-charts/barcharts'
import { markets } from '~/src/lib/markets-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import styles from '~/src/styles/markets-symbol-page.module.scss'
import * as timestamps from '~/src/utils/timestamps'

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  return {
    props: {
      symbol: ctx.query.symbol,
    },
  }
}

const IndexSymbol = ({ symbol }) => {
  const { data } = kitcoQuery(
    markets.barchartsQuotes({
      variables: {
        symbols: symbol as any,
        timestamp: timestamps.current(),
      },
    }),
  )

  return (
    <Layout title={symbol}>
      <PageLayoutTwoColumns>
        <div>
          <IndexDetailTitleBlock data={data} />
          <section className={styles.chartBlock}>
            <Barcharts symbol={symbol} />
          </section>
          <section className={styles.infoBlock}>
            <IndexHighLowTable data={data} />
          </section>
          <section className={styles.infoBlock}>
            <MarketPageIndicesCell />
          </section>
        </div>
        <div>
          <AdvertisingSlot
            id={'right-rail-1'}
            className="mx-auto mb-10 h-[250px] w-[300px] desktop:mb-4 no-print"
          />
          <LatestNewsCell />
        </div>
      </PageLayoutTwoColumns>
    </Layout>
  )
}

export default IndexSymbol
