import clsx from 'clsx'
import type { GetServerSideProps, NextPage } from 'next'
import Head from 'next/head'
import { useEffect, useState } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { z } from 'zod'
import { LatestNewsSidebar } from '~/src/components-news/LatestNewsSidebar/LatestNewsSidebar'
import { PageHeader } from '~/src/components/ChartsTitle/ChartsTitle.component'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import LayoutJewelers from '~/src/components/LayoutJewelers/LayoutJewelers'
import { MiniLabel } from '~/src/components/MiniLabel/MiniLabel'
import PriceBlock from '~/src/components/PriceBlock/PriceBlock'
import { TradingViewChartWidget } from '~/src/components/TradingViewChartWidget/TradingViewChartWidget'
import { useCurrency } from '~/src/hooks/Currency/useCurrency'
import { allCryptos } from '~/src/lib/all-cryptos'
import { news } from '~/src/lib/news-factory.lib'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import styles from './symbol.module.scss'

function findBySymbol(symbol: string) {
  const c = allCryptos.find((x) => x.symbol === symbol)
  if (!c) return null
  return c
}

export const getServerSideProps = (async (ctx) => {
  const valid = z
    .string()
    .min(2)
    .max(20)
    .safeParse(ctx?.params?.symbol as string)

  const crypto = allCryptos.find(
    (x) => x.href === `/price/crypto/${ctx?.params?.symbol}`,
  )

  if (!valid.success || !crypto) {
    return {
      notFound: true,
    }
  }

  const { dehydratedState } = await ssrQueries({
    ctxRes: ctx.res,
    queries: [
      news.newsByCategoryGeneric({
        variables: {
          limit: 5,
          offset: 0,
          urlAlias: '/news/category/cryptocurrencies',
        },
      }),
    ],
  })

  return {
    props: {
      dehydratedState,
      name: crypto?.name,
      symbol: crypto?.symbol,
    },
  }
}) satisfies GetServerSideProps

const CryptoPriceSymbolPage: NextPage<{ name: string; symbol: string }> = (
  props,
) => {
  const [isClient, setIsClient] = useState(false)
  const currency = useCurrency()
  const cryptoName = findBySymbol(props.symbol).name

  useEffect(() => {
    setIsClient(true)
  }, [])

  if (!isClient) {
    return null
  }

  return (
    <LayoutJewelers
      title={`${cryptoName} Price in USD | Real Time ${cryptoName} Chart | KITCO CRYPTO`}
    >
      <Head>
        <meta
          name="description"
          content={`The Kitco ${cryptoName} price index provides the latest ${cryptoName} price in US Dollars using an average from the world's leading exchanges.`}
        />
      </Head>
      <div className="relative mx-auto box-border w-full max-w-full px-5 md:w-[975px] md:px-[15px] desktop:w-[1290px]">
        <PageHeader.Root>
          <PageHeader.Title href="/price/crypto">crypto</PageHeader.Title>
          <PageHeader.Divider />
          <PageHeader.SubTitle>{cryptoName}</PageHeader.SubTitle>
        </PageHeader.Root>
        <MiniLabel name={cryptoName} />
        <div className={clsx('block md:gap-[15px]', styles.tabletGridOrder)}>
          <div className="w-full">
            <ErrBoundary>
              <PriceBlock symbol={props.symbol} />
            </ErrBoundary>
            <AdvertisingSlot
              id={'right-rail-2'}
              className="mx-auto mb-10 h-[250px] w-[300px]
              desktop:mb-4 desktop:h-[600px] no-print"
            />
          </div>
          <div className="relative flex min-h-[400px] flex-col">
            <TradingViewChartWidget
              symbol={`${props.symbol}${currency?.symbol ?? 'USD'}`}
            />
            <AdvertisingSlot
              id={'oop'}
              className="mx-auto flex w-[336px] h-[280px] justify-center
                desktop:min-h-[439px] desktop:min-w-[100%] no-print"
            />
          </div>
          <div className="block md:hidden desktop:block">
            <AdvertisingSlot
              id={'right-rail-1'}
              className={
                'mx-auto hidden min-h-[250px] w-[300px] desktop:flex no-print'
              }
            />
            <div className="mt-[15px]">
              <LatestNewsSidebar category="cryptocurrencies" />
            </div>
          </div>
        </div>
      </div>
      <AdvertisingSlot
        id={'footer'}
        className="after:font-['Font Awesome 5 Pro]' fixed bottom-0 left-1/2
            z-20
            w-[320px]
            -translate-x-1/2 after:absolute after:right-0 after:top-[-25px] after:w-[20px] after:cursor-pointer after:rounded after:bg-[#373737] after:text-center after:text-lg after:font-bold after:leading-[1.1] after:text-white after:content-['X'] tablet:h-[90px] tablet:w-[728px] desktop:hidden no-print"
      />
    </LayoutJewelers>
  )
}

export default CryptoPriceSymbolPage
