import { useQuery } from '@tanstack/react-query'
import type { GetServerSideProps, NextPage } from 'next'
import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3'
import LeadgenFrom from '~/src/components/LeadgenForm'
import type { LeadGen } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import { ssrQueries } from '~/src/utils/ssr-wrappers'

export const getServerSideProps: GetServerSideProps = async (c) => {
  const title = c.query.title as string
  const fullSlug = `/leadgen/companies/${title}`

  const { dehydratedState } = await ssrQueries({
    ctxRes: c.res,
    queries: [
      news.nodeByUrlAlias({
        variables: {
          urlAlias: fullSlug,
        },
      }),
    ],
  })

  return {
    props: {
      dehydratedState,
      urlAlias: fullSlug,
    },
  }
}

const LeadgenCompaniesTitle: NextPage<{ urlAlias: string }> = ({
  urlAlias,
}) => {
  const { data } = useQuery(
    news.nodeByUrlAlias({
      variables: { urlAlias },
    }),
  )
  const leadgenData = data.nodeByUrlAlias as LeadGen

  return (
    <GoogleReCaptchaProvider
      reCaptchaKey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY}
      scriptProps={{
        async: false,
        defer: false,
        appendTo: 'head',
        nonce: undefined,
      }}
    >
      <div dangerouslySetInnerHTML={{ __html: leadgenData?.body }} />
      <div className="m-auto py-5" style={{ maxWidth: 650 }}>
        <LeadgenFrom dataProps={{ title: leadgenData?.title, urlAlias }} />
      </div>
    </GoogleReCaptchaProvider>
  )

  // return <div dangerouslySetInnerHTML={{ __html: leadgenData.body }}></div>
}

export default LeadgenCompaniesTitle
