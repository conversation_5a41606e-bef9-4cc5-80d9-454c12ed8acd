import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import LoginForm, { FormState } from '~/src/components/Auth/Form/LoginForm'
import LayoutLanding from '~/src/components/LayoutLanding/LayoutLanding'

const ActionPage = () => {
  // Get the router
  const router = useRouter()

  // Get the mode, oobCode, continueUrl, and lang from the router query
  // "lang" not yet implemented
  const { mode, oobCode, continueUrl } = router.query

  // State for the form state
  const [formState, setFormState] = useState<FormState>(FormState.LOGIN)

  // State for the title
  const [title, setTitle] = useState<string>('Kitco Auth')

  /**
   * Set the form state based on the mode and oobCode
   */
  useEffect(() => {
    if (mode && oobCode) {
      handleAction(mode as string)
    }
  }, [mode, oobCode])

  /**
   * Handle the action
   *
   * @param {string} mode
   * @returns {void}
   */
  const handleAction = (mode: string): void => {
    switch (mode) {
      case 'verifyEmail':
        setFormState(FormState.VERIFY_EMAIL_CODE)
        setTitle('Verify Account')
        break
      case 'resetPassword':
        setFormState(FormState.RESET_PASSWORD)
        setTitle('Reset Password')
        break
      case 'recoverEmail':
        setFormState(FormState.RECOVER_EMAIL)
        setTitle('Recover Email')
        break
      default:
        setFormState(FormState.LOGIN)
    }
  }

  /**
   * Handle the success of the form
   */
  const handleOnSuccess = () => {
    router.push((continueUrl as string) ?? '/')
  }

  return (
    <LayoutLanding title={title}>
      <LoginForm
        logo="/kitco_forum_logo.png"
        link={process.env.NEXT_PUBLIC_DISCOURSE_URL}
        defaultState={formState}
        onSuccess={handleOnSuccess}
        redirectToForum={true}
        oobCode={oobCode as string}
      />
    </LayoutLanding>
  )
}

export default ActionPage
