import dayjs, { type Dayjs } from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import type MarketStatus from '~/src/types/Market/MarketStatus'
import getMarketSchedule from '~/src/utils/Market/getMarketSchedule'

dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(isBetween)

/**
 * Determines the market's current status (open or closed) based on the provided timestamp.
 * If no timestamp is provided, it defaults to the current time.
 *
 * @param {Dayjs} [timestamp] - The timestamp to check the market status for. Defaults to the current time.
 * @returns {MarketStatus} - An object containing whether the market is open and the relevant open and close times.
 */
function getMarketStatus(timestamp: Dayjs = null): MarketStatus {
  // If no timestamp is provided, use the current time
  const currentTimestamp = timestamp
    ? timestamp.tz(process.env.NEXT_PUBLIC_TIMEZONE)
    : dayjs().tz(process.env.NEXT_PUBLIC_TIMEZONE)

  // Get the market schedule
  const { openHour, closeHour, openDays } = getMarketSchedule()

  let marketOpen = false
  let marketOpenTime: Dayjs | null = null
  let marketCloseTime: Dayjs | null = null

  // Search the past 7 days to find the relevant market period
  for (let n = 0; n <= 7; n++) {
    const candidateDate = currentTimestamp.subtract(n, 'day')
    const candidateDay = candidateDate.day()

    // Check if the current day is a market open day
    if (openDays.includes(candidateDay)) {
      const potentialMarketOpenTime = candidateDate
        .set('hour', openHour)
        .set('minute', 0)
        .set('second', 0)
        .set('millisecond', 0)
        .tz(process.env.NEXT_PUBLIC_TIMEZONE)

      // Determine if the market closes on the same day or the next day
      const closeDayOffset = closeHour > openHour ? 0 : 1

      const potentialMarketCloseTime = potentialMarketOpenTime
        .add(closeDayOffset, 'day')
        .set('hour', closeHour)
        .set('minute', 0)
        .set('second', 0)
        .set('millisecond', 0)
        .tz(process.env.NEXT_PUBLIC_TIMEZONE)

      // Check if the current timestamp is within the market open and close times
      if (
        currentTimestamp.isBetween(
          potentialMarketOpenTime,
          potentialMarketCloseTime,
          null,
          '[]',
        )
      ) {
        marketOpen = true
        marketOpenTime = potentialMarketOpenTime
        marketCloseTime = potentialMarketCloseTime
        break
      }

      // If the market is closed and we are past the close time
      if (currentTimestamp.isAfter(potentialMarketCloseTime)) {
        marketOpen = false
        marketOpenTime = potentialMarketOpenTime
        marketCloseTime = potentialMarketCloseTime
        break
      }
    }
  }

  // If no market period is found in the last 7 days, take the most recent known period
  if (marketOpenTime === null || marketCloseTime === null) {
    const lastOpenDay = openDays[openDays.length - 1]
    const lastMarketOpenDay =
      currentTimestamp.day() >= lastOpenDay
        ? currentTimestamp.day(lastOpenDay).tz(process.env.NEXT_PUBLIC_TIMEZONE)
        : currentTimestamp
            .subtract(1, 'week')
            .day(lastOpenDay)
            .tz(process.env.NEXT_PUBLIC_TIMEZONE)

    const potentialMarketOpenTime = lastMarketOpenDay
      .set('hour', openHour)
      .set('minute', 0)
      .set('second', 0)
      .set('millisecond', 0)
      .tz(process.env.NEXT_PUBLIC_TIMEZONE)

    const closeDayOffset = closeHour > openHour ? 0 : 1

    const potentialMarketCloseTime = potentialMarketOpenTime
      .add(closeDayOffset, 'day')
      .set('hour', closeHour)
      .set('minute', 0)
      .set('second', 0)
      .set('millisecond', 0)
      .tz(process.env.NEXT_PUBLIC_TIMEZONE)

    marketOpenTime = potentialMarketOpenTime
    marketCloseTime = potentialMarketCloseTime
    marketOpen = false
  }

  let chartDayStart: Dayjs
  let chartDayEnd: Dayjs

  if (marketOpenTime.day() === currentTimestamp.day()) {
    chartDayStart = marketOpenTime.clone().startOf('day')
    chartDayEnd = marketOpenTime.clone().endOf('day')
  } else {
    chartDayStart = marketCloseTime.clone().startOf('day')
    chartDayEnd = marketCloseTime.clone().endOf('day')
  }

  return {
    chartDayEnd,
    chartDayStart,
    marketCloseTime,
    marketOpen,
    marketOpenTime,
  }
}

export default getMarketStatus
