import { type FC, startTransition, useEffect, useState } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import useSWR from 'swr'
import LatestNewsCell from '~/src/components-news/LatestNewsCell/LatestNewsCell'
import Layout from '~/src/components/Layout/Layout'
import MetalStocksTable from '~/src/components/MetalStocksTable/MetalStocksTable'
import QuotesTable from '~/src/components/QuotesTable/QuotesTable'
import { Barcharts } from '~/src/features/bar-charts/barcharts'

const fetcher = (url: string) => fetch(url).then((r) => r.json())

const MetalsLanding: FC = () => {
  const { data: metalsData, error: metalsError } = useSWR(
    '/api/metalsgainers',
    fetcher,
  )
  const { data: etfData, error: etfError } = useSWR(
    '/api/getQuote/SLV,IAU,GLD,PPLT,PALL',
    fetcher,
  )
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    startTransition(() => {
      if (etfData || etfError) {
        setIsLoading(false)
      }
    })
  }, [etfData, etfError])

  if (metalsError || etfError) {
    return (
      <Layout title="Metals">
        <div className="px-4 sm:px-8 lg:px-0">
          <h1 className="text-2xl font-bold">Error loading data</h1>
          <p>There was an error loading the data. Please try again later.</p>
        </div>
      </Layout>
    )
  }

  return (
    <Layout title="Metals">
      <div className="grid gap-8 px-4 sm:grid-cols-1 sm:px-8 lg:grid-cols-layout-2 lg:px-0">
        <section>
          <div className="mb-8 grid gap-6 sm:grid-cols-1 sm:gap-0 lg:grid-cols-2">
            <Barcharts
              symbol="^XAUUSD"
              title="Gold"
              href="/markets/futures/^XAUUSD"
            />
            <div className="hidden md:block lg:block">
              <Barcharts
                symbol="^XAGUSD"
                title="Silver"
                href="/markets/futures/^XAUUSD"
              />
            </div>
          </div>
          <div className="mb-8">
            <MetalStocksTable
              title="Metals and Mining Gainers"
              data={metalsData?.gainers}
            />
          </div>
          <div className="mb-8">
            <MetalStocksTable
              title="Metals and Mining Losers"
              data={metalsData?.losers}
            />
          </div>
          <div className="mb-8">
            <MetalStocksTable
              title="Metals and Mining 52 Week Gainers"
              data={metalsData?.fiftytwowkgainers}
            />
          </div>
          <div className="mb-8">
            <MetalStocksTable
              title="Metals and Mining 52 Week Losers"
              data={metalsData?.fiftytwowklosers}
            />
          </div>
          <div className="mb-8">
            <QuotesTable
              title="Metal ETFs"
              section="stocks"
              data={etfData?.results}
              isLoading={isLoading}
            />
          </div>
        </section>
        <div>
          <AdvertisingSlot
            id={'right-rail-1'}
            className={
              'mx-auto mb-8 flex h-[250px] w-[300px] items-center justify-center no-print'
            }
          />
          <LatestNewsCell />
          <AdvertisingSlot
            id={'right-rail-2'}
            className={
              'mx-auto mb-8 mt-8 flex h-[600px] w-[300px] items-center justify-center no-print'
            }
          />
        </div>
      </div>
    </Layout>
  )
}

export default MetalsLanding
