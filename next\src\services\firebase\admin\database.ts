import type { UserRecord } from 'firebase-admin/auth'
import type { User } from 'firebase/auth'
import type { UserData } from '~/src/components/Auth/Types/UserData'
import { sanitizeUsername } from '~/src/features/auth/sanitize'
import { getDatabase } from './config'

/**
 * The name of the users table in the database.
 */
const usersTable: string = 'users'

/**
 * Checks if the given username exists in the Firebase Realtime Database using Admin SDK.
 * @param desiredUsername
 */
export const checkUsernameExists = async (desiredUsername: string) => {
  if (!desiredUsername) {
    return false
  }

  // Sanitize the username
  const { lowercase } = sanitizeUsername(desiredUsername)

  if (!lowercase) {
    return false
  }

  const db = getDatabase()

  const usersRef = db.ref(usersTable)
  const usernameQuery = usersRef.orderByChild('username').equalTo(lowercase)

  const snapshot = await usernameQuery.once('value')
  return snapshot.exists()
}

/**
 * Fetches the username for a specific user from Firebase Realtime Database using Firebase Admin.
 * Returns the username if found, or null if the user does not exist.
 *
 * @returns The username as a string or null.
 * @param user - A User, UserRecord, or string representing the user UID.
 */
export const getUsername = async (
  user: User | UserRecord | string,
): Promise<UserData> => {
  const db = getDatabase()

  // If the user is a string, assume it's the UID; otherwise, get the UID from the user object
  const uid = typeof user === 'string' ? user : user.uid

  try {
    const snapshot = await db.ref(`${usersTable}/${uid}`).once('value')

    if (!snapshot.exists()) {
      return null
    }

    const userData = snapshot.val()

    return {
      username: userData.username || null,
      displayUsername: userData.displayUsername || null,
    }
  } catch (error) {
    console.error('Failed to fetch username from Firebase:', error)
    return null
  }
}
