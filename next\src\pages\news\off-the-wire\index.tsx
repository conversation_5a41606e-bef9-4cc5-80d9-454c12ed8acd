import { type FC, Fragment, useCallback } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { TeaserTextWithAuthor } from '~/src/components-news/ArticleTeasers/TeaserTextWithAuthor'
import { TeaserTextWithAuthorNoSummary } from '~/src/components-news/ArticleTeasers/TeaserTextWithAuthorNoSummary'
import GlobalMeta from '~/src/components/GlobalMeta/GlobalMeta'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { Spacer } from '~/src/components/spacer/spacer.component'
import type { NewsOtwListQuery } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import useScreenSize from '~/src/utils/useScreenSize'

export type TVariables = {
  urlAlias: string
  limit: number
  offset: number
}

export async function getServerSideProps({ res }) {
  const variables = {
    limit: 50,
    offset: 0,
  }

  const { dehydratedState } = await ssrQueries({
    ctxRes: res,
    queries: [news.newsOTWList({ variables }), news.newsCategoriesTree()],
  })

  return {
    props: {
      dehydratedState,
      variables,
    },
  }
}

type Data = NewsOtwListQuery['nodeList']['items']

const OffTheWirePage: FC<{ variables: TVariables }> = ({ variables }) => {
  const { data } = kitcoQuery(
    news.newsOTWList({
      variables: variables,
      options: {
        enabled: true,
        select: useCallback((d: NewsOtwListQuery) => {
          const filteredArr = d?.nodeList.items?.filter(
            (obj) => Object.keys(obj).length !== 0,
          )
          return {
            ...d,
            nodeList: {
              total: d?.nodeList?.total,
              items: [...filteredArr],
            },
          }
        }, []),
      },
    }),
  )

  const items = data?.nodeList?.items as Data

  return (
    <LayoutNewsLanding title="Aggregated financial and geopolitical stories from the world | KITCO">
      <GlobalMeta />
      <div className="mx-auto box-border w-full max-w-full px-[20px] md:px-10 lg:px-10 xl:w-[1080px] xl:px-0">
        <h1 className="text-[32px] uppercase leading-[57.6px] text-[#111111] md:text-[48px]">
          OFF THE WIRE
        </h1>
        <Spacer className="h-5" />
        <ContentOffTheWirePage data={items} />
      </div>
    </LayoutNewsLanding>
  )
}
export default OffTheWirePage

const ContentOffTheWirePage: FC<{ data: Data }> = ({ data }) => {
  const AdsOnDesktop = () => {
    const { isDesktop } = useScreenSize()

    if (!isDesktop) return null

    return (
      <>
        <div className="w-[300px]">
          <AdvertisingSlot
            id={'right-rail-1'}
            className={'top-4 mx-auto mb-[30px] h-[250px] w-[300px]'}
          />
          <div className="mb-[30px] border border-[#E5E5E5] p-5 text-sm font-normal leading-[21px] no-print">
            Kitco News features Reuter&apos;s top financial, economic and
            geopolitical news making headlines around the world.
          </div>
          <AdvertisingSlot
            id={'right-rail-2'}
            className={'sticky top-4 mx-auto h-[600px] w-[300px] no-print'}
          />
        </div>
      </>
    )
  }

  return (
    <Fragment>
      <div className="flex gap-10 md:pt-5">
        <div>
          <FirstSection data={data?.slice(0, 5)} />
          <SecondSection data={data?.slice(5)} />
        </div>
        <AdsOnDesktop />
      </div>
    </Fragment>
  )
}

const FirstSection: FC<{ data: any }> = ({ data }) => {
  const { isMobile } = useScreenSize()

  if (isMobile) {
    return (
      <div className="flex">
        <div>
          {data?.map((node: any, idx: number) => (
            <Fragment key={idx}>
              <div className="mb-[15px] border-b border-ktc-borders pb-[15px]">
                <TeaserTextWithAuthorNoSummary
                  node={node}
                  size="lg"
                  aspectRatio="16x9"
                  key={node.id}
                  hideCategory={true}
                />
              </div>
            </Fragment>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="flex pb-[50px]">
      <div>
        {data?.map((node: any, idx: number) => (
          <Fragment key={idx}>
            <TeaserTextWithAuthor
              node={node}
              size="lg"
              aspectRatio="16x9"
              key={node.id}
            />
          </Fragment>
        ))}
      </div>
    </div>
  )
}

const SecondSection: FC<{ data: any }> = ({ data }) => {
  const { isDesktop } = useScreenSize()
  let adCounter = 1

  function advertInjector(idx: number) {
    if (idx % 5 === 0 && idx !== 0 && adCounter < 4) {
      adCounter++
      return true
    }
  }

  return (
    <div className="justify-between pb-[80px]">
      {data?.map((node: any, idx: number) => (
        <Fragment key={idx}>
          <div className="mb-[15px] border-b border-ktc-borders pb-[15px]">
            <TeaserTextWithAuthorNoSummary
              node={node}
              size="lg"
              aspectRatio="16x9"
              key={node.id}
              hideCategory={true}
            />
          </div>
          {advertInjector(idx) && !isDesktop && (
            <AdvertisingSlot
              id={`banner-${adCounter}`}
              className={
                'mx-auto my-10 h-[250px] w-[300px] md:h-[90px] md:w-[728px] no-print'
              }
            />
          )}
        </Fragment>
      ))}
    </div>
  )
}
