import { dehydrate } from '@tanstack/react-query'
import { AdvertisingSlot } from 'react-advertising'
import ChartsMeta from '~/src/components/ChartsMeta/ChartsMeta'
import { ChartsTitle } from '~/src/components/ChartsTitle/ChartsTitle.component'
import DetailPageChart from '~/src/components/Commodity/DetailPageChart/DetailPageChart'
import LayoutJewelers from '~/src/components/LayoutJewelers/LayoutJewelers'
import {
  getCurrencyQuery,
  getMetalQuoteQuery,
} from '~/src/hooks/MetalQuotes/useMetalQuoteCell'
import { preciousMetals } from '~/src/lib/metals'
import { kitcoQueryClient } from '~/src/services/database/kitcoQuery'
import * as timestamps from '~/src/utils/timestamps'
import { titleCase } from '~/src/utils/titleCase'

/**
 * Get the server side props
 *
 * @param params
 */
export async function getServerSideProps({ params }) {
  // If the name is not provided, return a 404
  if (!params?.name) {
    return {
      notFound: true,
    }
  }

  // Find the symbol in the URL
  const symbol = preciousMetals.find(
    (x) => x.name === params.name.toLowerCase(),
  )?.symbol

  // If the symbol is not found, return
  if (!symbol) {
    console.error('Symbol not found', symbol)
    return {
      notFound: true,
    }
  }

  const queryClient = kitcoQueryClient()

  // Prefetch the queries
  await queryClient.prefetchQuery(getCurrencyQuery())
  // We use USD as the default currency because we can't access the currency from the redux store in SSR
  await queryClient.prefetchQuery(getMetalQuoteQuery(symbol, 'USD'))

  return {
    props: {
      dehydratedState: JSON.parse(JSON.stringify(dehydrate(queryClient))),
      name: params.name,
      ssrTimestamp: timestamps.current(),
      symbol,
    },
  }
}

/**
 * Get the title metadata for the page
 *
 * @param pageName - The name of the page (e.g. gold, silver, etc.)
 */
const titleMetaDataPage = (pageName: string) => {
  const page = titleCase(pageName)
  if (page === 'Rhodium')
    return 'Rhodium Price Today | Price of Rhodium Per Ounce | Rhodium Chart | KITCO'

  return `${page} Price Today | Price of ${page} Per Ounce | 24 Hour Spot Chart | KITCO`
}

/**
 * Commodity page component props
 */
interface CommodityPageProps {
  name: string
  symbol: string
  ssrTimestamp: number
}

/**
 * Commodity page component
 *
 * @param name - The name of the commodity
 * @param symbol - The symbol of the commodity
 * @param ssrTimestamp - The server side rendering timestamp
 * @constructor
 */
const CommodityPage = ({ name, symbol, ssrTimestamp }: CommodityPageProps) => {
  return (
    <LayoutJewelers title={titleMetaDataPage(name)}>
      <ChartsMeta nameChart={titleCase(name ?? 'Gold')} />

      <div className="mx-auto box-border w-full max-w-full px-5 md:w-[975px] md:px-[15px] desktop:w-[1290px]">
        <ChartsTitle />

        <DetailPageChart
          name={name}
          symbol={symbol}
          ssrTimestamp={ssrTimestamp}
        />
      </div>

      <AdvertisingSlot
        id={'chartFooter'}
        className="after:font-['Font Awesome 5 Pro]' fixed bottom-0 left-1/2
            z-20
            w-[320px]
            -translate-x-1/2 after:absolute after:right-0 after:top-[-25px] after:w-[20px] after:cursor-pointer after:rounded after:bg-[#373737] after:text-center after:text-lg after:font-bold after:leading-[1.1] after:text-white after:content-['X'] tablet:h-[90px] tablet:hidden desktop:hidden no-print"
      />
    </LayoutJewelers>
  )
}

export default CommodityPage
