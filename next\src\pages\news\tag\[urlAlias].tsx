import { clsx } from 'clsx'
import type { GetServerSideProps, NextPage } from 'next'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { type FC, useEffect } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { IoChevronDownOutline } from 'react-icons/io5'
import { TeaserCard } from '~/src/components-news/ArticleTeasers/TeaserCard'
import { TeaserCardForNewParent } from '~/src/components-news/ArticleTeasers/TeaserCardForNewParent'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { GenericNewsList } from '~/src/components/generic-news-list/generic-news-list.component'
import { TrendingTags } from '~/src/components/trending-tags/trending-tags.component'
import type {
  ArticleTeaserFragmentFragment,
  NewsGenericByTagQuery,
  Tag,
} from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import cs from '~/src/utils/cs'
import { useInfinite, useParams } from '~/src/utils/infiniteScroll'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import styles from './news-alias-page.module.scss'

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const { dehydratedState } = await ssrQueries({
    ctxRes: ctx.res,
    queries: [
      news.tagByUrlAlias({
        variables: { urlAlias: `/news/tag/${ctx.query.urlAlias}` },
      }),
      news.nodeListByTag({
        variables: {
          limit: 10,
          offset: 0,
          urlAlias: `/news/tag/${ctx.query.urlAlias}`,
        },
        options: { enabled: true },
      }),
      news.newsCategoriesTree(),
    ],
  })

  return {
    props: {
      dehydratedState,
      urlAlias: ctx.query.urlAlias,
    },
  }
}

const useData = (urlAlias: string) => {
  const { params, incrementParams } = useParams(8)
  const { data, isFetching, isLoading } = kitcoQuery(
    news.nodeListByTag({
      variables: { ...params, urlAlias },
      options: { enabled: true },
    }),
  )

  const { items, isNextPage, fetchMore, loading } = useInfinite({
    items: data?.nodeListByTag?.items,
    incrementParams,
    total: data?.nodeListByTag?.total,
  })

  return {
    data: items ?? data?.nodeListByTag?.items,
    loading: loading || isLoading || isFetching,
    loadMore: {
      isDisabled: !isNextPage,
      execute: fetchMore,
    },
  }
}

const NewsAliasPage: NextPage<{ urlAlias: string }> = ({ urlAlias }) => {
  const router = useRouter()

  const { data, refetch } = kitcoQuery(
    news.tagByUrlAlias({
      variables: { urlAlias: `/news/tag/${urlAlias}` },
    }),
  )

  useEffect(() => {
    if (data.tagByUrlAlias === null) {
      router.push('/notfound')
      return null
    }
  }, [])

  useEffect(() => {
    refetch()
  }, [urlAlias])

  const tag = data?.tagByUrlAlias

  return (
    <LayoutNewsLanding title={`${tag?.name} | KITCO`}>
      <Head>
        <meta
          name="description"
          content="KITCO Covers The Latest Gold News, Silver News, Live Gold Prices, Silver Prices, Gold Charts, Gold Rate, Mining News, ETF, FOREX, Bitcoin, Crypto, Stock Markets"
        />
      </Head>
      <TagAliasWrapper tag={tag} />
    </LayoutNewsLanding>
  )
}

export default NewsAliasPage

const TagAliasWrapper: FC<{ tag: Tag }> = ({ tag }) => {
  return (
    <>
      <div className="w-full">
        {/* Desktop Layout */}
        <div className={styles.desktopLayout}>
          <TagAliasDesktop tag={tag} />
        </div>

        {/* Tablet Layout */}
        <div className={styles.tabletLayout}>
          <TagAliasTablet tag={tag} />
        </div>

        {/* Mobile Layout */}
        <div className={styles.mobileLayout}>
          <TagAliasMobile tag={tag} />
        </div>
      </div>
    </>
  )
}

const TagAliasDesktop: FC<{ tag: Tag }> = ({ tag }) => {
  const { data, loadMore, loading } = useData(tag?.urlAlias)

  return (
    <>
      <div className="mx-auto w-[1240px]">
        <TagTitle name={tag?.name} />
        {loading ? (
          <>
            <ThreeColumnLoading />
            <FiveColumnLoading />
          </>
        ) : (
          !data?.length && <EmptyContent tagName={tag?.name} />
        )}
        <ThreeColumn data={data?.slice(0, 3)} />
        <FiveColumn data={data?.slice(3)} />

        <LoadMore loadMore={loadMore} />
      </div>
      <TrendingTags />
      <div className="mx-auto w-[1240px] pt-10">
        <h2 className="font-mulish text-[32px] font-semibold leading-[42px]">
          Latest News
        </h2>
        <LatestNews />
      </div>
    </>
  )
}

const TagAliasTablet: FC<{ tag: Tag }> = ({ tag }) => {
  const { data, loadMore, loading } = useData(tag?.urlAlias)

  return (
    <>
      <div className="mx-auto w-full px-10">
        <TagTitle name={tag?.name} />
        {loading ? (
          <>
            <FourColumnLoading />
            <FourColumnLoading />
          </>
        ) : (
          !data?.length && <EmptyContent tagName={tag?.name} />
        )}
        <FourColumn data={data?.slice(0, 4)} />
        <FourColumn data={data?.slice(4)} />

        <AdvertisingSlot
          id={'banner-2'}
          className={'mx-auto bg-red-400 md:h-[90px] md:w-[728px] no-print'}
        />

        <LoadMore loadMore={loadMore} />
      </div>

      <TrendingTags />
      <div className="mx-auto w-full px-10 py-10">
        <h2 className="font-mulish text-[24px] font-semibold leading-[32px]">
          Latest News
        </h2>
        <LatestNews />
      </div>
    </>
  )
}

const TagAliasMobile: FC<{ tag: Tag }> = ({ tag }) => {
  const { data, loadMore, loading } = useData(tag?.urlAlias)

  return (
    <>
      <div className="mx-auto w-full px-5">
        <TagTitle name={tag?.name} />
        {loading ? (
          <>
            <SkeletonTeaserCard size="sm" />
            <MobileSkeleton />
            <MobileSkeleton />
          </>
        ) : (
          !data?.length && <EmptyContent tagName={tag?.name} />
        )}
        <GenericNewsList data={data} disableAdverts={true} />
        <LoadMore loadMore={loadMore} />
      </div>
      <TrendingTags />
      <div className="mx-auto w-full p-5 pt-10 md:p-10 lg:p-10">
        <h2 className="font-mulish pb-5 text-[24px] font-semibold leading-[32px]">
          Latest News
        </h2>
        <LatestNews />
      </div>
    </>
  )
}

const TagTitle: FC<{ name: string }> = ({ name }) => {
  return (
    <h1 className="text-[32px] uppercase leading-[38px] md:mb-5 md:text-[48px] md:leading-[58px]">
      <span
        className="text-kitco-black text-opacity-20"
        style={{ fontFamily: 'Bebas Neue' }}
      >
        TAG:&nbsp;
      </span>
      {name}
    </h1>
  )
}

const ThreeColumn: FC<{
  data: NewsGenericByTagQuery['nodeListByTag']['items']
}> = ({ data }) => {
  return (
    <div className="grid grid-cols-3 gap-10 pb-10">
      {data?.map((x: ArticleTeaserFragmentFragment) => (
        <TeaserCardForNewParent size="md" node={x} key={x.id} />
      ))}
    </div>
  )
}

const FourColumn: FC<{
  data: NewsGenericByTagQuery['nodeListByTag']['items']
}> = ({ data }) => {
  return (
    <div className="grid grid-cols-4 gap-[22px] pb-10">
      {data?.map((x: ArticleTeaserFragmentFragment) => (
        <TeaserCard size="sm" node={x} key={x.id} sizeImg="md" />
      ))}
    </div>
  )
}

const FiveColumn: FC<{
  data: NewsGenericByTagQuery['nodeListByTag']['items']
}> = ({ data }) => {
  return (
    <div className="grid grid-cols-5 gap-10">
      {data?.map((x: ArticleTeaserFragmentFragment) => (
        <TeaserCard
          size="sm"
          node={x}
          key={x.id}
          sizeImg="md"
          lineClampTitle="line-clamp-2"
        />
      ))}
    </div>
  )
}

const LoadMore: FC<{ loadMore: ReturnType<typeof useData>['loadMore'] }> = ({
  loadMore,
}) => {
  return (
    <div className="pb-10 md:py-9">
      {!loadMore.isDisabled ? (
        <button
          className={clsx(
            'flex w-full items-center justify-center gap-2 rounded-md border border-ktc-borders py-2 text-base',
          )}
          type="button"
          onClick={loadMore.execute}
        >
          <span className="font-medium leading-5 text-[#111111]">
            Load More
          </span>
          <IoChevronDownOutline />
        </button>
      ) : null}
    </div>
  )
}

const LatestNews: FC = () => {
  const { params, incrementParams } = useParams(10)
  const { data } = kitcoQuery(
    news.nodeListQueue({
      variables: { ...params, queueId: 'latest_news' },
      options: {
        enabled: true,
      },
    }),
  )

  const { ref, items, loading } = useInfinite({
    items: data?.queue?.items,
    incrementParams,
    total: data?.queue?.total,
  })

  return (
    <div>
      <GenericNewsList
        data={!items.length ? data?.queue?.items : items}
        disableAdverts={true}
      />
      <div ref={ref}>{loading && <div>Loading...</div>}</div>
    </div>
  )
}

const EmptyContent: FC<{ tagName: string }> = ({ tagName }) => {
  return (
    <div className="py-40 text-center">
      <p className="text-2xl font-semibold">
        Sorry, there are no articles related to “<strong>{tagName}</strong>” to
        show.
      </p>
    </div>
  )
}

const ThreeColumnLoading: FC = () => {
  return (
    <div className="grid grid-cols-3 gap-10 pb-10">
      {Array.from(Array(3).keys()).map((x) => (
        <SkeletonTeaserCard size="md" key={x} />
      ))}
    </div>
  )
}

const FourColumnLoading: FC = () => {
  return (
    <div className="grid grid-cols-4 gap-[22px] pb-10">
      {Array.from(Array(4).keys()).map((x) => (
        <SkeletonTeaserCard size="sm" key={x} />
      ))}
    </div>
  )
}

const FiveColumnLoading: FC = () => {
  return (
    <div className="grid grid-cols-5 gap-10">
      {Array.from(Array(5).keys()).map((x) => (
        <SkeletonTeaserCard size="sm" key={x} />
      ))}
    </div>
  )
}

const SkeletonTeaserCard = ({ size }) => {
  const heightImage = {
    sm: '',
    md: 'xl:h-[242px]',
  }

  return (
    <div className="w-full">
      <div
        className={cs([
          heightImage[size],
          'relative mt-1 aspect-video w-full animate-pulse rounded-lg bg-gray-200 object-cover',
        ])}
      />
      <div className="h-2" />
      <div className={'h-4 w-40 animate-pulse bg-gray-200'} />
      <div className="h-2" />
      <div className={'h-14 animate-pulse bg-gray-200'} />
      <div className="h-2" />
      <div className={'h-14 animate-pulse bg-gray-200'} />
      <div className="h-2" />
      <div className={'h-4 w-40 animate-pulse bg-gray-200'} />
    </div>
  )
}

const MobileSkeleton = () => {
  return (
    <div className="mb-5 flex h-[90px] w-full">
      <div className="relative relative block block aspect-video aspect-video w-[120px] animate-pulse rounded-lg bg-gray-200" />
      <div className="block w-[calc(100%_-_120px)] pl-4 pl-[14px]">
        <div className={'h-4 w-20 animate-pulse bg-gray-200'} />
        <div className="h-2" />
        <div className={'h-10 animate-pulse bg-gray-200'} />
        <div className="h-2" />
        <div className={'h-4 w-40 animate-pulse bg-gray-200'} />
      </div>
    </div>
  )
}
