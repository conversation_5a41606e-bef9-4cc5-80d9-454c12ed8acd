import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import getMarketSchedule from '~/src/utils/Market/getMarketSchedule'
import getMarketStatus from '~/src/utils/Market/getMarketStatus'

dayjs.extend(isBetween)
dayjs.extend(utc)
dayjs.extend(timezone)

// Mocking the getMarketSchedule function
jest.mock('~/src/utils/Market/getMarketSchedule')

describe('getMarketStatus', () => {
  const timezoneName = 'America/New_York'

  beforeEach(() => {
    jest.clearAllMocks() // Clear mocks before each test
    jest.resetAllMocks() // Reset any mocks or spy functions
    jest.restoreAllMocks() // Restore any spies created
    process.env.NEXT_PUBLIC_TIMEZONE = timezoneName // Reset timezone to default before each test
  })

  /**
   * Test case when the market is open.
   */
  it('should return market open when current timestamp is within market open hours', () => {
    // Mock the market schedule
    ;(getMarketSchedule as jest.Mock).mockReturnValue({
      openHour: 9, // Market opens at 9 AM
      closeHour: 17, // Market closes at 5 PM
      openDays: [1, 2, 3, 4, 5], // Monday to Friday
    })

    // Provide a timestamp when the market is open (Tuesday at 10:00 AM)
    const currentTimestamp = dayjs.tz('2023-10-17T10:00:00', timezoneName)

    const result = getMarketStatus(currentTimestamp)

    expect(result.marketOpen).toBe(true)
    expect(result.marketOpenTime.format('YYYY-MM-DD HH:mm:ss')).toBe(
      '2023-10-17 09:00:00',
    ) // Market opens at 9 AM
    expect(result.marketCloseTime.format('YYYY-MM-DD HH:mm:ss')).toBe(
      '2023-10-17 17:00:00',
    ) // Market closes at 5 PM
  })

  /**
   * Test case when the market is closed but we are past the market close time.
   */
  it('should return market closed when the current timestamp is after market close time', () => {
    // Mock the market schedule
    ;(getMarketSchedule as jest.Mock).mockReturnValue({
      openHour: 9, // Market opens at 9 AM
      closeHour: 17, // Market closes at 5 PM
      openDays: [1, 2, 3, 4, 5], // Monday to Friday
    })

    // Provide a timestamp after market close time (Tuesday at 6:00 PM)
    const currentTimestamp = dayjs.tz('2023-10-17T18:00:00', timezoneName)

    const result = getMarketStatus(currentTimestamp)

    expect(result.marketOpen).toBe(false)
    expect(result.marketOpenTime.format('YYYY-MM-DD HH:mm:ss')).toBe(
      '2023-10-17 09:00:00',
    ) // Market opens at 9 AM
    expect(result.marketCloseTime.format('YYYY-MM-DD HH:mm:ss')).toBe(
      '2023-10-17 17:00:00',
    ) // Market closes at 5 PM
  })

  /**
   * Test case when the market is closed and we are before the market opens.
   */
  it('should return market closed when current timestamp is before market open time', () => {
    // Mock the market schedule
    ;(getMarketSchedule as jest.Mock).mockReturnValue({
      openHour: 9, // Market opens at 9 AM
      closeHour: 17, // Market closes at 5 PM
      openDays: [1, 2, 3, 4, 5], // Monday to Friday
    })

    // Provide a timestamp before market open time (Tuesday at 8:00 AM)
    const currentTimestamp = dayjs.tz('2023-10-17T08:00:00', timezoneName)

    const result = getMarketStatus(currentTimestamp)

    expect(result.marketOpen).toBe(false)
    expect(result.marketOpenTime.format('YYYY-MM-DD HH:mm:ss')).toBe(
      '2023-10-16 09:00:00',
    ) // Market opens at 9 AM
    expect(result.marketCloseTime.format('YYYY-MM-DD HH:mm:ss')).toBe(
      '2023-10-16 17:00:00',
    ) // Market closes at 5 PM
  })

  /**
   * Test case when no timestamp is provided (current time is used).
   */
  it('should use the current timestamp if no timestamp is provided', () => {
    const now = dayjs().tz(timezoneName)

    // Mock the market schedule
    ;(getMarketSchedule as jest.Mock).mockReturnValue({
      openHour: now.clone().subtract(2, 'hour').hour(), // Market opens 2 hours ago
      closeHour: now.clone().add(5, 'hour').hour(), // Market closes 5 hours from now
      openDays: [
        now.clone().subtract(1, 'day').day(),
        now.clone().subtract(2, 'day').day(),
        now.day(),
        now.clone().add(1, 'day').day(),
        now.clone().add(2, 'day').day(),
      ], // It adds some days before and after the current day
    })

    // Mock the current time as Monday at 10:00 AM
    jest.spyOn(dayjs, 'tz').mockReturnValue(now)

    const result = getMarketStatus()

    expect(result.marketOpen).toBe(true)
    expect(result.marketOpenTime.format('YYYY-MM-DD HH:mm:ss')).toBe(
      now
        .clone()
        .subtract(2, 'hour')
        .minute(0)
        .second(0)
        .format('YYYY-MM-DD HH:mm:ss'),
    ) // Market opens 2 hours ago
    expect(result.marketCloseTime.format('YYYY-MM-DD HH:mm:ss')).toBe(
      now
        .clone()
        .add(5, 'hour')
        .minute(0)
        .second(0)
        .format('YYYY-MM-DD HH:mm:ss'),
    ) // Market closes 5 hours from now
  })

  /**
   * Test case when the current day is not a market day (weekend).
   */
  it('should return market closed when current day is a weekend', () => {
    // Mock the market schedule
    ;(getMarketSchedule as jest.Mock).mockReturnValue({
      openHour: 9, // Market opens at 9 AM
      closeHour: 17, // Market closes at 5 PM
      openDays: [1, 2, 3, 4, 5], // Monday to Friday
    })

    // Provide a timestamp on a Saturday
    const currentTimestamp = dayjs.tz('2023-10-14T12:00:00', timezoneName) // Saturday

    const result = getMarketStatus(currentTimestamp)

    expect(result.marketOpen).toBe(false)
  })

  /**
   * Test case when the market is closed, but we are checking on a future market day.
   */
  it('should return the previous market open and close times if the market is currently closed', () => {
    // Mock the market schedule
    ;(getMarketSchedule as jest.Mock).mockReturnValue({
      openHour: 9, // Market opens at 9 AM
      closeHour: 17, // Market closes at 5 PM
      openDays: [1, 2, 3, 4, 5], // Monday to Friday
    })

    // Provide a timestamp when the market is closed (Friday at 6:00 PM)
    const result = getMarketStatus(
      dayjs.tz('2023-10-13T18:00:00', timezoneName),
    ) // After close on Friday

    expect(result.marketOpen).toBe(false)
    expect(result.marketOpenTime.format('YYYY-MM-DD HH:mm:ss')).toBe(
      '2023-10-13 09:00:00',
    ) // Next market open is Monday 9 AM
    expect(result.marketCloseTime.format('YYYY-MM-DD HH:mm:ss')).toBe(
      '2023-10-13 17:00:00',
    ) // Market closes Monday at 5 PM
  })
})
