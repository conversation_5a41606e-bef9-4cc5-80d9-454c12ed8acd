.styleTable {
  // Override any external table styles
  &.responsiveTable,
  &.table,
  & {
    th,
    td {
      padding: 0.7em 0.25em !important;
      line-height: 1.5 !important;
    }
  }

  .styleThead {
    font-size: 0.95em;

    th {
      padding: 0.7em 0.25em !important;
      vertical-align: middle !important;
    }
  }

  tbody {
    td {
      padding: 0.7em 0.25em !important;
      vertical-align: middle !important;
    }

    tr:nth-of-type(odd) {
      background-color: #f5f5f5;
    }
  }

  @media screen and (max-width: 40em) {
    tbody tr {
      border: 1px solid #f5f5f5 !important;
    }
  }
}

.flag {
  width: 23px;
  height: 16px;
  margin-right: 0.25em;
}

.name {
  display: flex;
  align-items: center;
  border-right: solid 1px #e5e5e5;
  padding: 3px;
}

.time {
  margin-left: 0.5em;
  font-size: 0.825em;
  opacity: 0.75;
  white-space: nowrap;
}

.lastItemMidColumn {
  border-right: solid 1px #e5e5e5;
  padding-right: 0.25em;
}

// Global override for this specific table
:global(.kitco-exchange-rates-table th),
:global(.kitco-exchange-rates-table td) {
  padding: 0.7em 0.25em !important;
  line-height: 1.5 !important;
  vertical-align: middle !important;
}
