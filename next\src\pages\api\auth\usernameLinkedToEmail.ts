import type { NextApiRequest, NextApiResponse } from 'next'
import { sanitizeEmail, sanitizeUsername } from '~/src/features/auth/sanitize'
import { getUsername } from '~/src/services/firebase/admin/database'
import { getUserByEmail } from '~/src/services/firebase/admin/service'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method !== 'POST') {
    res.status(405).json({ message: 'Method Not Allowed' })
    return
  }

  const { email, username } = req.body

  const sanitizedEmail = sanitizeEmail(email)
  const { lowercase: sanitizedUsername } = sanitizeUsername(username)

  if (!sanitizedEmail || !sanitizedUsername) {
    res.status(400).json({ message: 'Email and username are required' })
    return
  }

  try {
    const user = await getUserByEmail(sanitizedEmail)

    if (!user) {
      res.status(404).json({ message: 'User not found' })
      return
    }

    // Check if the username matches the email
    const userData = await getUsername(user)

    if (userData?.username === sanitizedUsername) {
      res.status(200).json({ message: 'Username matches the provided email' })
    } else {
      res
        .status(404)
        .json({ message: 'Username does not match the provided email' })
    }
  } catch (error) {
    console.error('Server error:', error)
    res.status(500).json({ message: 'Server error' })
  }
}
