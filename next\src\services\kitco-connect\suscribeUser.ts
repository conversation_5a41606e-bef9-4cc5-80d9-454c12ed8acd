/**
 * Subscribes a user to Kitco Connect
 * @param {string} email - User's email address
 * @param {string} ipAddress - User's IP address
 * @param {number} channels - Subscription channels
 * @param {number} sourceID - Subscription source ID
 */
export const subscribeUser = async (
  email: string,
  ipAddress: string = null,
  channels = 27,
  sourceID = 8,
) => {
  try {
    // Get the user's IP address if not provided
    let ip: string = ipAddress
    if (!ipAddress) {
      ip = await getUserIP()
    }

    // Create the subscription URL
    const subscribeUrl = `https://connect.kitco.com/subscription/ws/subscribe/?sourceId=${sourceID}&channels=${channels}&email=${encodeURIComponent(email)}&ipAddress=${ip}`

    // Subscribe the user
    const response = await fetch(subscribeUrl)

    if (!response.ok) {
      throw new Error(`Subscription failed with status: ${response.status}`)
    }
    const data = await response.json()
    console.log('Subscription successful:', data)
  } catch (error) {
    console.error('Subscription failed:', error)
  }
}

/**
 * Gets the user's IP address
 */
export const getUserIP = async (): Promise<string> => {
  try {
    const response = await fetch('/api/getUserIP')
    if (!response.ok) {
      throw new Error('Failed to get IP address')
    }
    const data = await response.json()
    return data.ip
  } catch (error) {
    console.error('Failed to get IP address:', error)
    return 'undefined' // Default value if IP can't be obtained
  }
}
