import { sign } from 'jsonwebtoken'
import type { NextApiRequest, NextApiResponse } from 'next'
import type UserClaims from '~/src/services/coraltalk/UserClaims'

/**
 * CoralTalk token payload interface
 */
interface Payload {
  exp: number
  iat: number
  user: UserClaims
}

/**
 * Create a CoralTalk token for the user
 *
 * @param req : NextApiRequest The request object
 * @param res : NextApiResponse The response object
 */
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).end(`Method ${req.method} Not Allowed`)
  }

  // Validate the request body
  const userClaims: UserClaims = req.body

  // Generate the token payload for CoralTalk
  const payload: Payload = {
    exp: Math.floor(Date.now() / 1000) + 3600, // Token expires in 1 hour
    iat: Math.floor(Date.now() / 1000),
    user: userClaims,
  }

  // Get the JWT secret from the environment
  const secret = process.env.CORALTALK_JWT_SECRET

  // If the secret is not found, return an error
  if (!secret) {
    console.error('CoralTalk: JWT Secret not found')
    return res.status(500).send('CoralTalk: JWT Secret not found')
  }

  // Sign the token and return it
  try {
    const token = sign(payload, secret, { algorithm: 'HS256' })
    return res.status(200).json({ token })
  } catch (error) {
    console.error('CoralTalk: Error generating token:', error)
    return res.status(500).send('CoralTalk: Error generating token')
  }
}
