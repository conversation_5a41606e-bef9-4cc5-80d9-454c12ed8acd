import currency from 'currency.js'

const T_OUNCE_TO = {
  GRAM: 31.1035,
  KILO: 1000,
  TOLA: 11.7,
  OZ: 31.1035,
} as const

const opts = { symbol: '' }

function priceToOz(bid: number): string {
  return currency(bid, opts).multiply(T_OUNCE_TO.OZ).format()
}

function priceToGram(bid: number): string {
  return currency(bid, opts).format()
}

function priceToKilo(bid: number): string {
  return currency(bid, opts).multiply(T_OUNCE_TO.KILO).format()
}

function priceToTola(bid: number): string {
  return currency(bid, opts).multiply(T_OUNCE_TO.TOLA).format()
}

export const convert = {
  priceToOz,
  priceToGram,
  priceToKilo,
  priceToTola,
}

export type TConvertPrice = typeof convert
