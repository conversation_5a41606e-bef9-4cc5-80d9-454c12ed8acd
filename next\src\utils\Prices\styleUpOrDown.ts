import { isUp } from '~/src/utils/Prices/isUp'
import cs from '~/src/utils/cs'

/**
 * Function to style the up or down arrow based on the change percentage
 *
 * @param {number} percentChange - The change percentage.
 * @param {string} styles - The CSS class names.
 */
export const styleUpOrDown = (percentChange: number, styles) => {
  const convertedValue = Number.parseFloat(percentChange?.toString())
  if (convertedValue === 0) {
    return styles.unchanged
  }

  return !isUp(percentChange) ? cs([styles.down]) : cs([styles.up])
}
