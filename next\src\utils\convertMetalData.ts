import type { GoldIndexTableQuery, GoldIndexWidgetQuery } from '~/src/generated'
import type MetalData from '~/src/types/DataTable/MetalData'
import type MetalResult from '~/src/types/DataTable/MetalResult'
import type MetalValues from '~/src/types/DataTable/MetalValues'

/**
 * Checks if a property is a direct property of an object (not inherited).
 * @param object - The object to check.
 * @param property - The property to check for.
 * @returns {boolean} - True if the property is a direct property, false otherwise.
 */
const isDirectProperty = (
  object: GoldIndexTableQuery | GoldIndexWidgetQuery,
  property: string,
): boolean => {
  return Object.prototype.hasOwnProperty.call(object, property)
}

/**
 * Converts the pricing data for a single metal.
 * @param metalData - The data for a single metal.
 * @param conversionRate - The rate used to convert the price data.
 * @returns {Record<string, any>} - The converted pricing data for the metal.
 */
const convertPricingData = (
  metalData: MetalValues,
  conversionRate: number,
): MetalValues => {
  if (
    !metalData ||
    Object.prototype.hasOwnProperty.call(metalData, 'results') === false ||
    !metalData.results
  ) {
    return metalData
  }

  const metalResults = metalData.results.map((result: MetalResult) => ({
    ...result,
    bid: result.bid * conversionRate,
    change: result.change * conversionRate,
    ask: result.ask * conversionRate,

    low: result?.low ? result.low * conversionRate : 0,
    high: result?.high ? result.high * conversionRate : 0,

    // Extra fields
    ChangeUSD: result?.ChangeUSD ? result.ChangeUSD * conversionRate : 0,
    ChangeTrade: result?.ChangeTrade ? result.ChangeTrade * conversionRate : 0,
  }))

  return {
    ...metalData,
    results: metalResults,
  }
}

/**
 * Converts the pricing data of various metals to a different currency based on a given conversion rate.
 * @param data - The raw data of metal prices; expected to contain multiple metals, each with their own pricing information.
 * @param conversionRate - The rate used to convert the price data from one currency to another.
 * @returns {AllMetalsQuoteQuery} - An object structured to match the AllMetalsQuoteQuery type, with all price data converted.
 */
const convertMetalData = (
  data: GoldIndexTableQuery | GoldIndexWidgetQuery,
  conversionRate: number,
): MetalData => {
  const convertedData: MetalData = {}

  for (const metalName in data) {
    if (isDirectProperty(data, metalName)) {
      convertedData[metalName] = convertPricingData(
        data[metalName],
        conversionRate,
      )
    }
  }

  return convertedData
}

export default convertMetalData
