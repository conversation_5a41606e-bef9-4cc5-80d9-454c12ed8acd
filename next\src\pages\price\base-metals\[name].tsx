import { AdvertisingSlot } from 'react-advertising'
import ChartLabel from '~/src/components/ChartLabel/ChartLabel'
import ChartsMeta from '~/src/components/ChartsMeta/ChartsMeta'
import { PageHeader } from '~/src/components/ChartsTitle/ChartsTitle.component'
import CommodityContentDetailChart from '~/src/components/CommodityContentDetailChart/CommodityContentDetailChart'
import LayoutJewelers from '~/src/components/LayoutJewelers/LayoutJewelers'
import { MiniLabel } from '~/src/components/MiniLabel/MiniLabel'
import { baseMetals } from '~/src/lib/metals'
import { metals } from '~/src/lib/metals-factory.lib'
import { news } from '~/src/lib/news-factory.lib'
import capitalizeFirstLetter from '~/src/utils/capitalizeFirstLetter'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import * as timestamps from '~/src/utils/timestamps'
import { titleCase } from '~/src/utils/titleCase'

export async function getServerSideProps({ res, params }) {
  const symbol = baseMetals.find((x) => x.name === params.name)?.symbol
  if (!symbol) {
    return {
      notFound: true,
    }
  }

  const { dehydratedState } = await ssrQueries({
    ctxRes: res,
    queries: [
      metals.metalQuote({
        variables: {
          symbol: symbol,
          currency: 'USD',
          timestamp: timestamps.current(),
        },
      }),
      news.newsByCategoryGeneric({
        variables: {
          urlAlias: '/news/category/commodities',
          limit: 5,
          offset: 0,
        },
      }),
    ],
  })

  // 60s cache control header
  res.setHeader('Cache-Control', 's-maxage=60, stale-while-revalidate')
  return {
    props: {
      dehydratedState,
      name: params.name,
      symbol,
      ssrTimestamp: timestamps.current(),
    },
  }
}

interface Props {
  name: string
  symbol: string
  ssrTimestamp: number
}

const CommodityPage = ({ name, symbol, ssrTimestamp }: Props) => {
  const props = {
    name,
    symbol,
    ssrTimestamp,
  }

  return (
    <LayoutJewelers title={`Price of ${titleCase(name)} per Pound`}>
      <ChartsMeta nameChart={titleCase(name ?? 'Gold')} />
      <div className="mx-auto box-border w-full max-w-full px-5 md:w-[975px] md:px-[15px] desktop:w-[1290px]">
        <PageHeader.Root>
          <PageHeader.Title href="/price/base-metals">
            BASE METALS
          </PageHeader.Title>
          <PageHeader.Divider />
          <PageHeader.SubTitle>{name}</PageHeader.SubTitle>
        </PageHeader.Root>
        <MiniLabel name={name} />
        <CommodityContentDetailChart
          {...props}
          labelComponent={
            <ChartLabel
              label={`${capitalizeFirstLetter(name)} Chart in Ton / USD`}
            />
          }
          hideLatestNewsHeader
          isBaseMetal
        />
      </div>
      <AdvertisingSlot
        id={'footer'}
        className="after:font-['Font Awesome 5 Pro]' fixed bottom-0 left-1/2
                  z-20
                  w-[320px]
                  -translate-x-1/2 after:absolute after:right-0 after:top-[-25px] after:w-[20px] after:cursor-pointer after:rounded after:bg-[#373737] after:text-center after:text-lg after:font-bold after:leading-[1.1] after:text-white after:content-['X'] tablet:h-[90px] tablet:w-[728px] desktop:hidden no-print"
      />
    </LayoutJewelers>
  )
}

export default CommodityPage
