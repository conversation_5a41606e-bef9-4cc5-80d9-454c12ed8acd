import dayjs from 'dayjs'

/**
 * Transform SangHai data
 *
 * @param data
 */
export const transformData = (data) => {
  // Get timestamp from data
  const timestamp =
    data?.GetShanghaiFixByYearV3?.results?.[0]?.timestamp || data?.timestamp

  // Convert timestamp to date
  const originalDate = dayjs.unix(timestamp)

  // Get latest timestamp
  const latestTimestamp = originalDate.endOf('day').unix()

  return {
    ...data,
    latestTimestamp,
  }
}
