import { ref, update } from '@firebase/database'
import type { User } from 'firebase/auth'
import type { UserData } from '~/src/components/Auth/Types/UserData'
import { sanitizeUserData } from '~/src/features/auth/sanitize'
import { getDatabase } from './config'

/**
 * The name of the users table in the database.
 */
const usersTable: string = 'users'

export const storeToken = (userId: string, type: string, token: string) => {
  const db = getDatabase()

  const expires = Date.now() + 172800000 // Expires in 48 hours
  const updates = {}
  updates[`/${usersTable}/${userId}/${type}/token`] = token
  updates[`/${usersTable}/${userId}/${type}/expires`] = expires

  return update(ref(db), updates)
}

/**
 * Saves the given user data to the database.
 * Throws an error if the save fails.
 *
 * @param user
 * @param data
 */
export const saveUserData = async (user: User, data: UserData) => {
  const db = getDatabase()

  await update(ref(db, `/${usersTable}/${user.uid}`), sanitizeUserData(data))
}
