@import './../../styles/article.scss';

.articleBodyStyles {
  & p {
    margin: 1em 0;
  }

  & blockquote {
    & p {
      margin: 0;
    }
  }
}

.articleBulletNews {
  margin-top: -10px;

  font-size: 18px;
  line-height: 110%;

  &::before {
    content: '';
    border-color: transparent #111;
    border-style: solid;
    border-width: 0.35em 0 0.35em 0.45em;
    display: block;
    width: 5px;
    height: 10px;
    left: -1em;
    top: 0.9em;
    position: relative;
  }
}

.articleWrapper {
  @include articleWrapper;
}

.exitsPresentationImage {
  margin-top: -1em;
}
