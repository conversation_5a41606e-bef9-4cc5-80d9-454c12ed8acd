import type { UseQueryOptions } from '@tanstack/react-query'
import { gql } from 'graphql-request'
import type {
  MiningPressReleaseQueueQuery,
  MiningPressReleaseQueueQueryVariables,
} from '~/src/generated'
import { graphs } from '../services/database/fetcher'
import type QueryArgs from '../types/QueryArgs'

export const mining = {
  nodeListPressReleaseQueue: (
    args: QueryArgs<
      MiningPressReleaseQueueQueryVariables,
      MiningPressReleaseQueueQuery
    >,
  ): UseQueryOptions<MiningPressReleaseQueueQuery> => {
    // @ts-ignore
    return {
      enabled: false,
      ...args?.options,
      queryKey: ['nodeListPressReleaseQueue', args?.variables],
      queryFn: async () =>
        await graphs.contentFetch(
          gql`
            query MiningPressReleaseQueue(
              $limit: Int
              $offset: Int
              $queueId: String! = "press_releases"
            ) {
              queue: nodeListQueue(
                limit: $limit
                offset: $offset
                queueId: $queueId
                bundles: [PressRelease]
              ) {
                items {
                  ... on PressRelease {
                    id
                    title
                    teaserHeadline
                    createdAt
                    updatedAt
                    url
                  }
                }
                total
              }

              ids: nodeIdsInQueue(queueId: $queueId)
            }
          `,
          args?.variables,
        ),
    }
  },
}
