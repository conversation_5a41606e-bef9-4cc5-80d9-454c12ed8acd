import type { NextPage } from 'next'
import Head from 'next/head'
import type { FC } from 'react'
import { Fragment, useCallback } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { TeaserCardForNewParent } from '~/src/components-news/ArticleTeasers/TeaserCardForNewParent'
import { TeaserTextOnly } from '~/src/components-news/ArticleTeasers/TeaserTextOnly'
import { TeaserWide } from '~/src/components-news/ArticleTeasers/TeaserWide'
import { TeaserWideForSubCategory } from '~/src/components-news/ArticleTeasers/TeaserWideForSubCategory'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import {
  GenericNewsListLayoutNewParent,
  GenericNewsListMining,
} from '~/src/components/generic-news-list/generic-news-list.component'
import { NewsCategoryTitle } from '~/src/components/news-category/news-category.component'
import { Spacer } from '~/src/components/spacer/spacer.component'
import type { NewsByCategoryGenericQuery } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import type { ArticlesUnion, TeasersUnion } from '~/src/types/types'
import { useInfinite, useParams } from '~/src/utils/infiniteScroll'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import StrippedString from '~/src/utils/strippedString'
import useScreenSize from '~/src/utils/useScreenSize'

export type TVariables = {
  urlAlias: string
  limit: number
  offset: number
}

export async function getServerSideProps({ res }) {
  const variables = {
    urlAlias: '/news/category/mining',
    limit: 20,
    offset: 0,
    includeRelatedCategories: true,
  }

  const { dehydratedState } = await ssrQueries({
    ctxRes: res,
    queries: [
      news.newsByCategoryGeneric({ variables }),
      news.newsCategoriesTree(),
    ],
  })

  return {
    props: {
      dehydratedState,
      variables,
    },
  }
}

type Data = NewsByCategoryGenericQuery['nodeListByCategory']['items']

const CategoryAliasPage: NextPage<{ variables: TVariables }> = ({
  variables,
}) => {
  const { params, incrementParams } = useParams(20)
  const { data } = kitcoQuery(
    news.newsByCategoryGeneric({
      variables: {
        ...params,
        urlAlias: variables?.urlAlias,
        includeRelatedCategories: true,
      },
      options: {
        enabled: true,
        select: useCallback((d: NewsByCategoryGenericQuery) => {
          const short = d?.nodeListByCategory
          return {
            nodeListByCategory: {
              ...short,
              items: short?.items?.filter(
                (x) => x?.__typename !== 'Commentary',
              ),
            },
          }
        }, []),
      },
    }),
  )

  const { ref, items, loading } = useInfinite({
    items: data?.nodeListByCategory?.items,
    incrementParams,
    total: data?.nodeListByCategory?.total,
  })

  const { isMobile, isTablet } = useScreenSize()

  let itemsOrData = !items?.length ? data?.nodeListByCategory?.items : items
  // Filter and handle teaserSnippet field missing
  itemsOrData = itemsOrData?.map((item) => {
    if (!item.teaserSnippet) {
      const pFirst = StrippedString(item?.source?.description)?.replace(
        '&nbsp;',
        ' ',
      )
      return { ...item, teaserSnippet: pFirst }
    }
    return { ...item }
  })

  if (isMobile) {
    return (
      <LayoutNewsLanding title="Mining News, Mining Stocks, Junior Mining | KITCO">
        <Head>
          <meta
            name="description"
            content="Latest Mining News, Mining Information, Mining Stories and Video from Kitco Mining News"
          />
        </Head>
        <div className="mx-auto box-border w-full max-w-full px-[15px] md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
          <NewsCategoryTitle />
          <CategoryAliasPageMobile data={itemsOrData} />
          <div ref={ref}>{loading && <div>Loading...</div>}</div>
        </div>
      </LayoutNewsLanding>
    )
  }

  if (isTablet) {
    return (
      <LayoutNewsLanding title="Mining News, Mining Stocks, Junior Mining | KITCO">
        <Head>
          <meta
            name="description"
            content="Latest Mining News, Mining Information, Mining Stories and Video from Kitco Mining News"
          />
        </Head>
        <div className="mx-auto box-border w-full max-w-full px-[20px] md:px-[20px] lg:px-10 xl:w-[1240px] xl:px-0">
          <NewsCategoryTitle />
          <CategoryAliasPageTablet data={itemsOrData} />
          <div ref={ref}>{loading && <div>Loading...</div>}</div>
        </div>
      </LayoutNewsLanding>
    )
  }

  return (
    <LayoutNewsLanding title="Mining News, Mining Stocks, Junior Mining | KITCO">
      <Head>
        <meta
          name="description"
          content="Latest Mining News, Mining Information, Mining Stories and Video from Kitco Mining News"
        />
      </Head>
      <div className="mx-auto box-border w-full max-w-full px-[20px] md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <NewsCategoryTitle />
        <CategoryAliasPageDesktop data={itemsOrData} />
        <div ref={ref}>{loading && <div>Loading...</div>}</div>
      </div>
    </LayoutNewsLanding>
  )
}

export default CategoryAliasPage

const CategoryAliasPageDesktop: FC<{ data: Data }> = ({ data }) => {
  return (
    <Fragment>
      <FirstSectionDesktop data={data?.slice(0, 6)} />
      <AdvertisingSlot
        id={'banner-2'}
        className="m-[20px_auto_15px_auto] h-[90px] w-[728px] no-print"
      />
      <SecondSectionDesktop data={data?.slice(6, 9)} />
      <div className="mt-[22px] flex w-full justify-center">
        <AdvertisingSlot
          id={'oop'}
          className="desktop:min-hxw-[485px] mx-auto mb-[15px] min-h-[90px] min-w-[800px] tablet:min-h-[420px] no-print"
        />
      </div>
      <ThirdSectionDesktop data={data?.slice(9, 12)} />
      <AdvertisingSlot
        id="mining-content-billboard"
        className="mx-auto my-[20px] flex min-h-[600px] w-[100%] max-w-[300px] items-center justify-center border-[1px] border-[#ccc] native-sm:h-[252px] native-sm:min-h-[200px] native-sm:max-w-[970px] no-print"
      />
      <Spacer className="h-5" />
      <GenericNewsListMining
        data={data?.slice(12, data?.length)}
        disableAdverts={true}
        hideCategory
      />
      <Spacer className="h-20" />
    </Fragment>
  )
}

const CategoryAliasPageTablet: FC<{ data: Data }> = ({ data }) => {
  return (
    <Fragment>
      <FirstSectionTablet data={data?.slice(0, 5)} />
      <SecondSectionTablet data={data?.slice(5, 8)} />
      <AdvertisingSlot
        id={'oop'}
        className={
          'mx-auto mb-[10px] mt-[20px] flex min-h-[90px] min-w-[100%] justify-center no-print'
        }
      />
      <ThirdSectionTablet data={data?.slice(8, 11)} />
      <AdvertisingSlot
        id="mining-content-billboard"
        className="mx-auto my-[20px] flex min-h-[600px] w-[100%] max-w-[300px] items-center justify-center border-[1px] border-[#ccc] native-sm:h-[252px] native-sm:min-h-[200px] native-sm:max-w-[970px] no-print"
      />
      <GenericNewsListLayoutNewParent
        data={data?.slice(11, data?.length)}
        hideCategory
        classTitle="mt-[-7px]"
      />
    </Fragment>
  )
}

const CategoryAliasPageMobile: FC<{ data: Data }> = ({ data }) => {
  return (
    <Fragment>
      <GenericNewsListLayoutNewParent data={data} hideCategory />
    </Fragment>
  )
}

const FirstSectionDesktop: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="flex border-b border-ktc-borders pb-8">
      <div className="w-6.5/10 border-r border-ktc-borders pr-[40px]">
        <TeaserCardForNewParent
          node={data?.[0] as ArticlesUnion}
          size="xl"
          hideCategory
        />
      </div>
      <div className="flex w-3.5/10 flex-col pl-[40px]">
        <div>
          {data
            ?.slice(1, data?.length)
            .map((node: TeasersUnion, idx: number) => {
              return (
                <div className="mb-[34px]" key={node.id ?? idx}>
                  <TeaserWide
                    node={node}
                    key={node.id}
                    size="md"
                    aspectRatio="auto"
                    hideCategory
                  />
                </div>
              )
            })}
        </div>
      </div>
    </div>
  )
}

const SecondSectionDesktop: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="flex justify-between gap-10 border-b border-ktc-borders py-10">
      <div className="w-1/3">
        <TeaserTextOnly
          node={data?.[0] as ArticlesUnion}
          size="md"
          hideCategory
        />
      </div>
      <div className="w-1/3">
        <TeaserTextOnly
          node={data?.[1] as ArticlesUnion}
          size="md"
          hideCategory
        />
      </div>
      <div className="w-1/3">
        <TeaserTextOnly
          node={data?.[2] as ArticlesUnion}
          size="md"
          hideCategory
        />
      </div>
    </div>
  )
}

const ThirdSectionDesktop: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="grid grid-cols-3 gap-10 border-b border-ktc-borders py-10">
      {data?.map((node: TeasersUnion) => (
        <TeaserCardForNewParent
          node={node}
          size="md"
          key={node.id}
          hideCategory
        />
      ))}
    </div>
  )
}

const ThirdSectionTablet: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="grid grid-cols-3 gap-10 border-b border-ktc-borders pb-10 pt-10">
      {data?.map((node: TeasersUnion) => (
        <TeaserCardForNewParent
          node={node}
          size="md"
          lineClamp="line-clamp-4"
          key={node.id}
          hideCategory
        />
      ))}
    </div>
  )
}

const FirstSectionTablet: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="border-b border-ktc-borders pb-1">
      <div className="pb-[48px]">
        <TeaserCardForNewParent
          node={data?.[0] as ArticlesUnion}
          size="xl"
          hideCategory
        />
      </div>
      <div className="flex flex-col border-t border-ktc-borders pt-10">
        <div>
          {data
            ?.slice(1, data?.length)
            .map((node: TeasersUnion, idx: number) => {
              if (idx === 3) {
                return (
                  <Fragment key={node.id ?? idx}>
                    <div className="mb-8" key={node.id ?? idx}>
                      <TeaserWideForSubCategory
                        node={node}
                        key={node.id}
                        size="lg"
                        aspectRatio="16x9"
                        hideCategory
                      />
                    </div>
                  </Fragment>
                )
              }
              return (
                <div className="mb-8" key={node.id ?? idx}>
                  <TeaserWideForSubCategory
                    node={node}
                    key={node.id}
                    size="lg"
                    aspectRatio="16x9"
                    hideCategory
                  />
                </div>
              )
            })}
        </div>
      </div>
    </div>
  )
}

const SecondSectionTablet: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="flex justify-between gap-10 border-b border-ktc-borders pb-10 pt-10">
      <div className="w-1/3">
        <TeaserTextOnly
          node={data?.[0] as ArticlesUnion}
          size="sm"
          lineClamp="line-clamp-2"
          hideCategory
        />
      </div>
      <div className="w-1/3">
        <TeaserTextOnly
          node={data?.[1] as ArticlesUnion}
          size="sm"
          lineClamp="line-clamp-2"
          hideCategory
        />
      </div>
      <div className="w-1/3">
        <TeaserTextOnly
          node={data?.[2] as ArticlesUnion}
          size="sm"
          lineClamp="line-clamp-2"
          hideCategory
        />
      </div>
    </div>
  )
}
