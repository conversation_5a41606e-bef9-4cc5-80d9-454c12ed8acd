import type { GetServerSideProps, NextPage } from 'next'
import { useMemo } from 'react'
import { ContentWrapper } from '~/src/components-news/ContentWrapper/ContentWrapper.component'
import { NewsVideosPagesCategoryAndDetailHeader } from '~/src/components-news/NewsPagesHeaders/NewsVideosPagesHeaders.component'
import { Playlist } from '~/src/components-news/Playlist/Playlist.component'
import {
  FeaturedVideo,
  Section,
  UpNext,
} from '~/src/components-news/VideoPagesTopSection/VideoPagesTopSection.component'
import { VideoPlaylistTeaser } from '~/src/components-news/VideoPlaylistTeaser/VideoPlaylistTeaser'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { Query } from '~/src/components/Query/Query'
import { vcms } from '~/src/lib/vcms-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import { Loaders } from '..'

export const getServerSideProps: GetServerSideProps = async (c) => {
  const urlAlias = `/${c.query.date}`
  if (!urlAlias) return { notFound: true }
  const { dehydratedState } = await ssrQueries({
    ctxRes: c.res,
    queries: [vcms.categories()],
  })

  return {
    props: {
      dehydratedState,
      urlAlias,
    },
  }
}

const NewsVideoCategoryPage: NextPage<{ urlAlias: string }> = ({
  urlAlias,
}) => {
  const { data } = kitcoQuery(
    vcms.categories({
      options: { enabled: true },
    }),
  )

  const currentCategory = useMemo(() => {
    return data?.VideoConsumerCategories?.find((x) => x.urlAlias === urlAlias)
  }, [data, urlAlias])

  const categoryById = vcms.categoryById({
    variables: { id: Number(currentCategory?.id) },
    options: { enabled: !!currentCategory?.id },
  })

  const feeds = vcms.feed({
    variables: {
      upNext: true,
      latest: true,
    },
  })

  const categoryVideos = vcms.categoryById({
    variables: { id: Number(currentCategory?.id) },
    options: { enabled: !!currentCategory?.id },
  })

  return (
    <LayoutNewsLanding title="Videos" enableDarkBG={true}>
      <div className="bg-[#192732] px-0 pt-[20px] text-white lg:px-8 lg:pt-[50px]">
        <ContentWrapper>
          <NewsVideosPagesCategoryAndDetailHeader
            routeLabel={currentCategory?.name}
          />
        </ContentWrapper>
      </div>
      <ErrBoundary>
        <Section>
          <Query fetcher={categoryById}>
            {(res) => (
              <FeaturedVideo
                isFetching={res.isFetching}
                node={
                  res?.data?.VideoConsumerCategoryById?.edges?.snippets?.[0]
                }
              />
            )}
          </Query>
          <Query fetcher={feeds}>
            {(res) => (
              <UpNext
                isFetching={res.isFetching}
                nodes={res?.data?.VideoConsumerFeed?.upNext?.slice(0, 4)}
              />
            )}
          </Query>
        </Section>
      </ErrBoundary>
      <div className="px-0 lg:px-8">
        <ContentWrapper className="border-b border-b-white/10 pb-10">
          <Playlist.Title>
            MORE {currentCategory?.name?.toUpperCase()}
          </Playlist.Title>
          <Query fetcher={categoryVideos}>
            {(res) => (
              <Playlist.Row>
                {res?.data?.VideoConsumerCategoryById?.edges?.snippets
                  ?.slice(1, 26)
                  .map((x) => (
                    <VideoPlaylistTeaser
                      node={x}
                      isFetching={res.isFetching}
                      key={x.id}
                    />
                  ))}
              </Playlist.Row>
            )}
          </Query>
        </ContentWrapper>
      </div>
      <div className="px-0 lg:px-8">
        <Query fetcher={feeds}>
          {(res) => (
            <ContentWrapper className="pb-10">
              <Playlist.Title>Latest Videos</Playlist.Title>
              <>
                <Playlist.Row>
                  {res.isFetching ? (
                    <Loaders />
                  ) : (
                    res?.data?.VideoConsumerFeed?.latest
                      ?.slice(1, 5)
                      ?.map((x) => <VideoPlaylistTeaser node={x} key={x.id} />)
                  )}
                </Playlist.Row>
                {!res?.data?.VideoConsumerFeed?.latest?.length && (
                  <p className="text-2xl text-white">
                    That&apos;s all the videos we have for now. Please check
                    back later.
                  </p>
                )}
              </>
            </ContentWrapper>
          )}
        </Query>
      </div>
    </LayoutNewsLanding>
  )
}

export default NewsVideoCategoryPage
