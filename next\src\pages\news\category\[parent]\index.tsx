import type { NextPage } from 'next'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { type FC, Fragment, useCallback, useEffect } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { TeaserCardForNewParent } from '~/src/components-news/ArticleTeasers/TeaserCardForNewParent'
import { TeaserTextOnly } from '~/src/components-news/ArticleTeasers/TeaserTextOnly'
import { TeaserWide } from '~/src/components-news/ArticleTeasers/TeaserWide'
import { TeaserWideForSubCategory } from '~/src/components-news/ArticleTeasers/TeaserWideForSubCategory'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import {
  GenericNewsList,
  GenericNewsListLayoutNewParent,
} from '~/src/components/generic-news-list/generic-news-list.component'
import { NewsCategoryTitle } from '~/src/components/news-category/news-category.component'
import { Spacer } from '~/src/components/spacer/spacer.component'
import type { NewsByCategoryGenericQuery } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import type { ArticlesUnion, TeasersUnion } from '~/src/types/types'
import { useInfinite, useParams } from '~/src/utils/infiniteScroll'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import StrippedString from '~/src/utils/strippedString'
import { urlSafePath } from '~/src/utils/url-safe-path'
import useScreenSize from '~/src/utils/useScreenSize'

export type TVariables = {
  urlAlias: string
  limit: number
  offset: number
}

export async function getServerSideProps({ query, res }) {
  // regex to make a string URL friendly
  const parentCategory = urlSafePath(query?.parent)

  const variables = {
    urlAlias: `/news/category/${parentCategory}`,
    limit: 20,
    offset: 0,
    includeRelatedCategories: true,
  }

  const { dehydratedState } = await ssrQueries({
    ctxRes: res,
    queries: [
      news.newsByCategoryGeneric({ variables }),
      news.newsCategoriesTree(),
    ],
  })

  return {
    props: {
      dehydratedState,
      variables,
    },
  }
}

type Data = NewsByCategoryGenericQuery['nodeListByCategory']['items']

const CategoryAliasPage: NextPage<{ variables: TVariables }> = ({
  variables,
}) => {
  const r = useRouter()

  // TODO: Temporarily hide the conferences page
  useEffect(() => {
    if (r.query.parent === 'conferences') {
      r.push('/news')
    }
  }, [r.query.parent])

  const parentName = (r.query.parent as string).toUpperCase()
  const { params, incrementParams } = useParams(20)
  const { data } = kitcoQuery(
    news.newsByCategoryGeneric({
      variables: {
        ...params,
        urlAlias: variables?.urlAlias,
        includeRelatedCategories: true,
      },
      options: {
        enabled: true,
        select: useCallback((d: NewsByCategoryGenericQuery) => {
          const short = d?.nodeListByCategory
          return {
            nodeListByCategory: {
              ...short,
              items: short?.items?.filter(
                (x) =>
                  x?.__typename !== 'Commentary' && Object.keys(x).length !== 0,
              ),
            },
          }
        }, []),
      },
    }),
  )

  const { ref, items, loading } = useInfinite({
    items: data?.nodeListByCategory?.items,
    incrementParams,
    total: data?.nodeListByCategory?.total,
  })

  const { isTablet, isMobile } = useScreenSize()

  let itemsOrData = !items?.length ? data?.nodeListByCategory?.items : items
  // Filter and handle teaserSnippet field missing
  itemsOrData = itemsOrData?.map((item) => {
    if (!item.teaserSnippet) {
      const pFirst = StrippedString(item?.source?.description)?.replace(
        '&nbsp;',
        ' ',
      )
      return { ...item, teaserSnippet: pFirst }
    }
    return { ...item }
  })

  if (isMobile) {
    return (
      <LayoutNewsLanding title={`${parentName} News | KITCO`}>
        <Head>
          <meta
            name="description"
            content="KITCO Covers The Latest Gold News, Silver News, Live Gold Prices, Silver Prices, Gold Charts, Gold Rate, Mining News, ETF, FOREX, Bitcoin, Crypto, Stock Markets"
          />
        </Head>
        <div className="mx-auto box-border w-full max-w-full px-[20px] md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
          <NewsCategoryTitle />
          <CategoryAliasPageMobile data={itemsOrData} />
          <div ref={ref}>{loading && <div>Loading...</div>}</div>
        </div>
        <AdvertisingSlot
          id={'footer'}
          className="after:font-['Font Awesome 5 Pro]' fixed bottom-0 left-1/2
                  z-20
                  w-[320px]
                  -translate-x-1/2 after:absolute after:right-0 after:top-[-25px] after:w-[20px] after:cursor-pointer after:rounded after:bg-[#373737] after:text-center after:text-lg after:font-bold after:leading-[1.1] after:text-white after:content-['X'] tablet:h-[90px] tablet:w-[728px] desktop:hidden no-print"
        />
      </LayoutNewsLanding>
    )
  }

  if (isTablet) {
    return (
      <LayoutNewsLanding title={`${parentName} News | KITCO`}>
        <Head>
          <meta
            name="description"
            content="KITCO Covers The Latest Gold News, Silver News, Live Gold Prices, Silver Prices, Gold Charts, Gold Rate, Mining News, ETF, FOREX, Bitcoin, Crypto, Stock Markets"
          />
        </Head>
        <div className="mx-auto box-border w-full max-w-full px-[20px] md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
          <NewsCategoryTitle />
          <CategoryAliasPageTablet data={itemsOrData} />
          <div ref={ref} />
        </div>
        <AdvertisingSlot
          id={'footer'}
          className="after:font-['Font Awesome 5 Pro]' fixed bottom-0 left-1/2
                  z-20
                  w-[320px]
                  -translate-x-1/2 after:absolute after:right-0 after:top-[-25px] after:w-[20px] after:cursor-pointer after:rounded after:bg-[#373737] after:text-center after:text-lg after:font-bold after:leading-[1.1] after:text-white after:content-['X'] tablet:h-[90px] tablet:w-[728px] desktop:hidden no-print"
        />
      </LayoutNewsLanding>
    )
  }

  return (
    <LayoutNewsLanding title={`${parentName} News | KITCO`}>
      <Head>
        <meta
          name="description"
          content="KITCO Covers The Latest Gold News, Silver News, Live Gold Prices, Silver Prices, Gold Charts, Gold Rate, Mining News, ETF, FOREX, Bitcoin, Crypto, Stock Markets"
        />
      </Head>
      <div className="mx-auto box-border w-full max-w-full px-[20px] md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <NewsCategoryTitle />
        <CategoryAliasPageDesktop data={itemsOrData} />
        <div ref={ref} />
      </div>
    </LayoutNewsLanding>
  )
}
export default CategoryAliasPage

const CategoryAliasPageDesktop: FC<{ data: Data }> = ({ data }) => {
  return (
    <Fragment>
      <FirstSectionDesktop data={data?.slice(0, 6)} />
      <AdvertisingSlot
        id={'banner-2'}
        className="m-[20px_auto_15px_auto] h-[90px] w-[728px] no-print"
      />
      <SecondSectionDesktop data={data?.slice(6, 9)} />
      <ThirdSectionDesktop data={data?.slice(9, 12)} />
      <Spacer className="h-5" />
      <GenericNewsList
        data={data?.slice(12, data?.length)}
        disableAdverts={true}
      />
      <Spacer className="h-20" />
    </Fragment>
  )
}

const CategoryAliasPageTablet: FC<{ data: Data }> = ({ data }) => {
  return (
    <Fragment>
      <FirstSectionTablet data={data?.slice(0, 5)} />
      <SecondSectionTablet data={data?.slice(5, 8)} />
      <ThirdSectionTablet data={data?.slice(8, 11)} />
      <GenericNewsListLayoutNewParent data={data?.slice(11, data?.length)} />
    </Fragment>
  )
}

const CategoryAliasPageMobile: FC<{ data: Data }> = ({ data }) => {
  return (
    <Fragment>
      <GenericNewsListLayoutNewParent data={data} />
    </Fragment>
  )
}

const FirstSectionDesktop: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="flex border-b border-ktc-borders pb-8">
      <div className="w-6.5/10 border-r border-ktc-borders pr-[40px]">
        <TeaserCardForNewParent node={data?.[0] as ArticlesUnion} size="xl" />
      </div>
      <div className="flex w-3.5/10 flex-col pl-[40px]">
        <div>
          {data
            ?.slice(1, data?.length)
            .map((node: TeasersUnion, idx: number) => {
              return (
                <div className="mb-[34px]" key={node.id ?? idx}>
                  <TeaserWide
                    node={node}
                    key={node.id}
                    size="md"
                    aspectRatio="auto"
                  />
                </div>
              )
            })}
        </div>
      </div>
    </div>
  )
}

const SecondSectionDesktop: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="flex justify-between gap-10 border-b border-ktc-borders py-10">
      <div className="w-1/3">
        <TeaserTextOnly node={data?.[0] as ArticlesUnion} size="md" />
      </div>
      <div className="w-1/3">
        <TeaserTextOnly node={data?.[1] as ArticlesUnion} size="md" />
      </div>
      <div className="w-1/3">
        <TeaserTextOnly node={data?.[2] as ArticlesUnion} size="md" />
      </div>
    </div>
  )
}

const ThirdSectionDesktop: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="grid grid-cols-3 gap-10 border-b border-ktc-borders py-10">
      {data?.map((node: TeasersUnion) => (
        <TeaserCardForNewParent node={node} size="md" key={node.id} />
      ))}
    </div>
  )
}

const ThirdSectionTablet: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="grid grid-cols-3 gap-10 border-b border-ktc-borders pb-10 pt-10">
      {data?.map((node: TeasersUnion) => (
        <TeaserCardForNewParent
          node={node}
          size="md"
          lineClamp="line-clamp-4"
          key={node.id}
        />
      ))}
    </div>
  )
}

const FirstSectionTablet: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="border-b border-ktc-borders pb-1">
      <div className="pb-[48px]">
        <TeaserCardForNewParent node={data?.[0] as ArticlesUnion} size="xl" />
      </div>
      <div className="flex flex-col border-t border-ktc-borders pt-10">
        <div>
          {data
            ?.slice(1, data?.length)
            .map((node: TeasersUnion, idx: number) => {
              if (idx === 3) {
                return (
                  <Fragment key={node.id ?? idx}>
                    <div className="mb-[34px]">
                      <AdvertisingSlot
                        id={'banner-1'}
                        className={
                          'mx-auto my-12 md:h-[90px] md:w-[728px] no-print'
                        }
                      />
                    </div>
                    <div className="mb-8" key={node.id ?? idx}>
                      <TeaserWideForSubCategory
                        node={node}
                        key={node.id}
                        size="lg"
                        aspectRatio="16x9"
                      />
                    </div>
                  </Fragment>
                )
              } else {
                return (
                  <div className="mb-8" key={node.id ?? idx}>
                    <TeaserWideForSubCategory
                      node={node}
                      key={node.id}
                      size="lg"
                      aspectRatio="16x9"
                    />
                  </div>
                )
              }
            })}
        </div>
      </div>
    </div>
  )
}

const SecondSectionTablet: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="flex justify-between gap-10 border-b border-ktc-borders pb-10 pt-10">
      <div className="w-1/3">
        <TeaserTextOnly
          node={data?.[0] as ArticlesUnion}
          size="sm"
          lineClamp="line-clamp-2"
        />
      </div>
      <div className="w-1/3">
        <TeaserTextOnly
          node={data?.[1] as ArticlesUnion}
          size="sm"
          lineClamp="line-clamp-2"
        />
      </div>
      <div className="w-1/3">
        <TeaserTextOnly
          node={data?.[2] as ArticlesUnion}
          size="sm"
          lineClamp="line-clamp-2"
        />
      </div>
    </div>
  )
}
