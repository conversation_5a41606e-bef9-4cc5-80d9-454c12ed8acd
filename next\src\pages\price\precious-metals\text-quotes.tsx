import dayjs from 'dayjs'
import type { NextPage } from 'next'
import Head from 'next/head'
import { Suspense } from 'react'
import { AiOutlinePrinter } from 'react-icons/ai'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import Layout from '~/src/components/Layout/Layout'
import LondonFix from '~/src/components/LondonFix/LondonFix'
import NewYorkSpot from '~/src/components/NewYorkSpot/NewYorkSpot'
import { Query } from '~/src/components/Query/Query'
import type { LondonFixQuery } from '~/src/generated'
import { metals } from '~/src/lib/metals-factory.lib'
import dates from '~/src/utils/dates'
import {
  type ExtendedMarketStatus,
  ssrGetMarketStatus,
} from '~/src/utils/market-status.util'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import * as timestamps from '~/src/utils/timestamps'

/**
 * Fetches server-side props for the page, including market status and file creation time.
 * @returns {Promise<{ props: { marketStatus: object, timeCreateFile: string } }>}
 */
export async function getServerSideProps(c) {
  // Set the time the file was created
  const timeCreateFile = dates.timeNow('h:mm:ss A EST on ddd MMMM D YYYY')

  // Fetch market status
  const status = await ssrGetMarketStatus()

  // Prefetch the queries
  const { dehydratedState } = await ssrQueries({
    ctxRes: c.res,
    queries: [
      metals.allMetalsQuote({
        variables: {
          currency: 'USD',
          timestamp: timestamps.current(),
        },
      }),
      metals.londonFixByYear({
        variables: {
          year: dayjs().year().toString(),
          currency: 'USD',
        },
        options: {},
      }),
    ],
  })

  return {
    props: {
      dehydratedState,
      marketStatus: status,
      timeCreateFile,
    },
  }
}

/**
 * Props for TextQuotes component.
 *
 * @interface TextQuotesProps
 * @property {ExtendedMarketStatus} marketStatus - The market status object.
 * @property {string} timeCreateFile - The time the file was created.
 */
interface TextQuotesProps {
  marketStatus: ExtendedMarketStatus
  timeCreateFile: string
}

/**
 * TextQuotes component for rendering market data and London Fix prices.
 * @param {{ marketStatus: MarketStatus, timeCreateFile: string }} props - The component props.
 * @returns {JSX.Element} The rendered component.
 */
const TextQuotes: NextPage = ({
  marketStatus,
  timeCreateFile,
}: TextQuotesProps) => {
  // Fetch data for all metals
  const metalDataFetcher = metals.allMetalsQuote({
    variables: {
      currency: 'USD',
      timestamp: timestamps.current(),
    },
  })

  // Fetch London Fix data
  const londonDataFetcher = metals.londonFixByYear({
    variables: {
      year: dayjs().year().toString(),
      currency: 'USD',
    },
    options: {},
  })

  /**
   * Triggers the print functionality in the browser.
   */
  const handlePrint = () => {
    if (window.print) {
      window.print()
    }
  }

  return (
    <Layout title="Text Only Precious Metals Quotes | KITCO">
      <Head>
        <meta
          name="description"
          content="Live Spot Prices for Gold, Silver, Platinum, Palladium and Rhodium."
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Space+Mono&display=swap"
          rel="stylesheet"
        />
        <style>{`
          .space-mono-font {
            font-family: 'Space Mono', monospace !important;
          }
      `}</style>
      </Head>
      <main className="space-mono-font page-print relative w-full px-[20px] xl:w-[767px] xl:px-0">
        <button
          className="no-print absolute right-[20px] inline-block text-center text-kitco-black xl:right-[-150px]"
          onClick={handlePrint}
          type="button"
        >
          <AiOutlinePrinter size={40} />
          Print
        </button>
        <div>
          <h1 className="mr-[45px] pb-1 text-2xl font-bold md:text-4xl">
            KITCO Metals Inc.
          </h1>
          <h2 className="mr-[45px] pb-8 text-xl md:text-2xl">
            Text Only Precious Metals Quotes
          </h2>
          <h3 className="text-xl">Spot Price</h3>
          <h4 className="text-xs tracking-tighter md:text-sm md:tracking-normal">
            <span className="mr-2 uppercase">{marketStatus?.statusString}</span>
            <span>{marketStatus?.timeToNextStatusString}</span>
          </h4>
          <ErrBoundary>
            <Suspense fallback={<div>Loading...</div>}>
              <Query fetcher={metalDataFetcher}>
                {({ data: metalData }) => (
                  <NewYorkSpot
                    metalData={metalData}
                    updatedAt={timeCreateFile}
                  />
                )}
              </Query>
            </Suspense>
          </ErrBoundary>
          <ErrBoundary>
            <Suspense fallback={<div>Loading...</div>}>
              <Query fetcher={londonDataFetcher}>
                {({ data: londonData }) => (
                  <LondonFix
                    londonData={londonData as LondonFixQuery}
                    timeCreateFile={timeCreateFile}
                  />
                )}
              </Query>
            </Suspense>
          </ErrBoundary>

          <div className="pt-10">
            <img
              src="/image-text-quotes.png"
              className="banner-print w-full md:w-[430px]"
              alt="Text Only Market Page"
            />
          </div>
        </div>
      </main>
    </Layout>
  )
}

export default TextQuotes
