import { onAuthStateChanged } from 'firebase/auth'
import { useRouter } from 'next/router'
import { useEffect } from 'react'
import EditProfile from '~/src/components/Auth/EditProfile'
import Layout from '~/src/components/Layout/Layout'
import { auth } from '~/src/services/firebase/config'

export default function Index() {
  const router = useRouter()

  useEffect(() => {
    // Wait for firebase auth to be initialized
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (!user) {
        router.push('/login')
      }
    })

    // Clean up the subscription
    return () => unsubscribe()
  }, [])

  return (
    <Layout title={'Edit Profile'}>
      <EditProfile />
    </Layout>
  )
}
