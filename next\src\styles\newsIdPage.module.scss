@use './vars' as *;

.grid {
  @include contentWrapper;
  display: grid;
  grid-template-columns: 1fr 300px;

  @media only screen and (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.gridColumnMain {
  padding: 0 10px;
}

.columnSidebar {
  padding: 0 10px;
}

.advertisement {
  height: 150px;
  background-color: #f5f5f5;
  margin-top: 2em;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart {
  height: 250px;
  margin-top: 2em;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.twitterfeed {
  height: 450px;
  margin-top: 2em;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}
