import dayjs from 'dayjs'
import isToday from 'dayjs/plugin/isToday'
import relativeTime from 'dayjs/plugin/relativeTime'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(relativeTime)
dayjs.extend(isToday)
const EST = process.env.NEXT_PUBLIC_TIMEZONE

export function teaserTimestamp(isoOrUnixStamp: string | number, fmt?: string) {
  if (typeof isoOrUnixStamp === 'number') {
    const unixToDayjs = dayjs.unix(isoOrUnixStamp)
    if (unixToDayjs.isToday()) {
      return unixToDayjs.tz(EST).fromNow()
    }
    return unixToDayjs.tz(EST).format(fmt ?? 'MMM DD, YYYY - h:mm A')
  }

  const isoToDayJs = dayjs(isoOrUnixStamp)
  if (isoToDayJs.isToday()) {
    return isoToDayJs.tz(EST).fromNow()
  }
  return isoToDayJs.tz(EST).format(fmt ?? 'MMM DD, YYYY - h:mm A')
}

export function teaserTimestampFromUTC(utcTimestamp: string, fmt?: string) {
  const isoToDayJs = dayjs.utc(utcTimestamp)
  if (isoToDayJs.isToday()) {
    return isoToDayJs.tz(EST).fromNow()
  }
  return isoToDayJs.tz(EST).format(fmt ?? 'MMM DD, YYYY - h:mm A')
}
