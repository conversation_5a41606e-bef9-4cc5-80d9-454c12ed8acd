export const getStorage = () => sessionStorage

export const saveScrollPos = (asPath: string) => {
  try {
    getStorage().setItem(
      `scrollPos:${asPath}`,
      JSON.stringify({ x: window.scrollX, y: window.scrollY }),
    )
  } catch {}
}

export const restoreScrollPos = (asPath: string) => {
  try {
    const json = getStorage().getItem(`scrollPos:${asPath}`)
    const scrollPos = json ? JSON.parse(json) : undefined
    if (scrollPos && scrollPos.y) {
      window.scrollTo(scrollPos.x, scrollPos.y)
    }
  } catch {}
}

export const deleteScrollPos = (asPath: string) => {
  try {
    getStorage().removeItem(`scrollPos:${asPath}`)
  } catch {}
}
