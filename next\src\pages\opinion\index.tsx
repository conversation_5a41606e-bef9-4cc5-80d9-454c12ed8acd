import clsx from 'clsx'
import type { GetServerSideProps } from 'next'
import Head from 'next/head'
import Image from 'next/image'
import Link from 'next/link'
import type { FC } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { TeaserTextOnlyWithAuthor } from '~/src/components-news/ArticleTeasers/TeaserTextOnlyWithAuthor'
import { ImageMS } from '~/src/components/ImageMS/ImageMS.component'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { GenericNewsListWithAuthor } from '~/src/components/generic-news-list/generic-news-list.component'
import { NewsCategoryTitle } from '~/src/components/news-category/news-category.component'
import type { Commentary, NewsTopContributorsQuery } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { useInfinite, useParams } from '~/src/utils/infiniteScroll'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import useScreenSize from '~/src/utils/useScreenSize'

export const getServerSideProps: GetServerSideProps = async ({ res }) => {
  const { dehydratedState } = await ssrQueries({
    ctxRes: res,
    queries: [
      news.newsCommentaries({ variables: { limit: 10, offset: 0 } }),
      news.newsCommentaries({ variables: { limit: 7, offset: 0 } }),
      news.newsCategoriesTree(),
      news.topContributors(),
    ],
  })
  return {
    props: {
      dehydratedState,
    },
  }
}

const OpinionsLanding: FC = () => {
  const { params, incrementParams } = useParams(10)
  const { data } = kitcoQuery(
    news.newsCommentaries({
      variables: { ...params },
      options: { enabled: true },
    }),
  )
  const { isMobile, isDesktop } = useScreenSize()

  const { ref, items, loading } = useInfinite({
    items: data?.commentaries?.items,
    incrementParams,
    total: data?.commentaries?.total,
  })

  const fetched = items as Commentary[]

  const { data: contributors } = kitcoQuery(news.topContributors())

  // Fetch only the first one
  const heroOpinion = fetched?.[0]

  // Fetch the next 3
  const topSectionOpinions = fetched?.slice(1, 4)

  return (
    <LayoutNewsLanding title="Gold, Silver, Platinum News, Opinion & Analysis | KITCO">
      <Head>
        <meta
          name="description"
          content="Kitco News is your daily source of opinion and analysis on Gold, Silver, Platinum, Palladium, The Fed, world markets and precious metals mining news."
        />
      </Head>
      <div className="mx-auto box-border w-full max-w-full px-5 md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <NewsCategoryTitle />
        {!isMobile ? (
          <FirstSectionDesktop
            heroOpinion={heroOpinion}
            topSectionOpinions={topSectionOpinions}
          />
        ) : (
          <FirstSectionMobile topOpinions={fetched?.slice(0, 4)} />
        )}

        {isDesktop && <TopContributors contributors={contributors} />}

        <GenericNewsListWithAuthor data={fetched?.slice(4)} />
        <div ref={ref}>{loading && <div>Loading...</div>}</div>
      </div>
    </LayoutNewsLanding>
  )
}

export default OpinionsLanding

const FirstSectionDesktop: FC<{
  heroOpinion: Commentary
  topSectionOpinions: Commentary[]
}> = ({ heroOpinion, topSectionOpinions }) => {
  const { isTablet } = useScreenSize()
  return (
    <div className="flex flex-col border-b border-ktc-borders pb-10 md:pb-0 lg:flex-row lg:pb-10">
      <div className="w-full border-0 border-ktc-borders md:border-b md:pb-[40px] lg:w-[53.4%] lg:border-0 lg:pb-0 lg:pr-[40px]">
        <div className="mb-2 overflow-hidden">
          <Link href={heroOpinion?.urlAlias ?? '/'}>
            <ImageMS
              src={
                heroOpinion?.teaserImage?.detail?.default?.srcset ??
                heroOpinion?.image?.detail?.default?.srcset ??
                heroOpinion?.legacyThumbnailImageUrl
              }
              hasLegacyThumbnailImageUrl={
                !!heroOpinion?.legacyThumbnailImageUrl
              }
              alt={`${heroOpinion?.title} teaser image`}
              priority={true}
              width={1202}
              height={676}
              service="icms"
              className={clsx(
                'w-full',
                'relative',
                'mb-2.5 aspect-video rounded-lg object-cover',
              )}
            />
          </Link>
        </div>
        <TeaserTextOnlyWithAuthor
          node={heroOpinion as any}
          size="xl"
          hideCategory={true}
          hideSummary={false}
        />
      </div>
      <div className="mt-10 flex w-full flex-col border-l-ktc-borders pl-0 lg:mt-0 lg:w-[calc(100%_-_53.4%_+_40px)] lg:border-l lg:pl-[40px]">
        <div className="flex flex-col gap-10">
          {topSectionOpinions.map((x) => (
            <TeaserTextOnlyWithAuthor
              node={x as any}
              size="md"
              hideCategory={true}
              hideSummary={false}
              key={x?.id}
            />
          ))}
        </div>
        {isTablet && (
          <AdvertisingSlot
            id={`banner-1`}
            className={
              'mx-auto my-[20px] block h-[90px] w-[728px] md:my-10 lg:hidden no-print'
            }
          />
        )}
      </div>
    </div>
  )
}

const FirstSectionMobile: FC<{ topOpinions: Commentary[] }> = ({
  topOpinions,
}) => {
  return (
    <div className="flex flex-col border-t border-t-ktc-borders pt-5">
      <div className="flex w-full flex-col border-l-ktc-borders pl-0 lg:mt-0 lg:w-1/2 lg:border-l lg:pl-[40px]">
        <div className="block">
          {topOpinions.map((x) => (
            <div
              className="mb-5 flex gap-5 border-b border-b-ktc-borders pb-5"
              key={x.id}
            >
              <div className="w-[120px] flex-initial">
                <Link href={x?.urlAlias ?? '/'}>
                  <ImageMS
                    src={
                      x?.image?.detail?.default?.srcset ??
                      x?.legacyThumbnailImageUrl
                    }
                    hasLegacyThumbnailImageUrl={!!x?.legacyThumbnailImageUrl}
                    alt={`${x?.title} teaser image`}
                    priority={true}
                    width={400}
                    height={340}
                    service="icms"
                    className={clsx(
                      'w-full',
                      'relative',
                      'mb-2.5 aspect-[4/3] rounded-lg',
                    )}
                  />
                </Link>
              </div>
              <div className="w-[calc(100%_-_140px)] flex-initial">
                <TeaserTextOnlyWithAuthor
                  node={x as any}
                  size="sm"
                  hideCategory={true}
                  hideSummary={true}
                  classTitle="mt-[-3px]"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

const TopContributors: FC<{ contributors: NewsTopContributorsQuery }> = ({
  contributors,
}) => {
  return (
    <>
      <h2 className="font-mulish my-5 text-[21px] uppercase leading-[27px]">
        Top Contributors
      </h2>

      <div className="flex gap-5 border-b border-b-ktc-borders pb-6">
        {!contributors ? (
          <h2>loading</h2>
        ) : (
          <>
            {contributors?.topContributors?.map((x) => (
              <Link
                className="flex w-full max-w-[120px] flex-col items-center"
                key={x.id}
                href={x.urlAlias ?? '#'}
              >
                <>
                  <div className="mx-auto text-center">
                    <Image
                      // loader={() => x.imageUrl ?? "/default-avatar.svg"}
                      src={x.imageUrl ?? '/default-avatar.svg'}
                      alt={`Photo of ${x.name}`}
                      width={80}
                      height={80}
                      layout="fixed"
                      className="rounded-full"
                    />
                  </div>
                  <div className="mt-1 text-center text-sm font-normal italic leading-[18px] text-black">
                    {x.name}
                  </div>
                </>
              </Link>
            ))}
          </>
        )}
      </div>
    </>
  )
}
