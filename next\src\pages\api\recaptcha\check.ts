import type { NextApiRequest, NextApiResponse } from 'next'

/**
 * Check if the reCAPTCHA token is valid and return a success response
 * If the token is invalid, return an error response
 * This function is used to verify the reCAPTCHA token on the server-side
 * before allowing the user to submit the form
 *
 * @description This function is used to verify the reCAPTCHA token on the server-side
 * before allowing the user to submit the form.
 *
 * @param {NextApiRequest} req - The incoming request
 * @param {NextApiResponse} res - The outgoing response
 * @returns {Promise<void>} - A promise that resolves when the verification is complete
 * @throws {Error} - If there is an error during the verification process
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
): Promise<void> {
  // Handle any other HTTP method
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST'])
    return res
      .status(405)
      .json({ success: false, message: `Method ${req.method} Not Allowed` })
  }

  try {
    // Get the reCAPTCHA token from the request body
    const { token } = req.body

    // Verify the token with Google's reCAPTCHA API
    const response = await fetch(
      `https://www.google.com/recaptcha/api/siteverify?secret=${process.env.RECAPTCHA_SECRET_KEY}&response=${token}`,
      {
        method: 'POST',
      },
    )
    const data = await response.json()

    if (data.success) {
      return res.status(200).json({ success: true })
    }
    return res
      .status(500)
      .json({ success: false, message: 'Verification failed' })
  } catch (error) {
    console.error('Error verifying reCAPTCHA:', error)
    return res
      .status(500)
      .json({ success: false, message: 'Verification failed' })
  }
}
