import type { UseQueryOptions } from '@tanstack/react-query'

/**
 * Query arguments
 *
 * This is a generic type that takes two type arguments: TVariables and TData.
 * And it returns an object with two properties: variables and options.
 * Omitting the queryKey and queryFn properties from the UseQueryOptions type.
 *
 *
 * @template TVariables - The type of the variables object
 * @template TData - The type of the data object
 *
 * @param variables
 * @param options
 *
 * @returns
 */
interface QueryArgs<TVariables, TData> {
  variables?: TVariables
  options?: Omit<UseQueryOptions<TData, unknown, TData>, 'queryKey' | 'queryFn'>
}

export default QueryArgs
