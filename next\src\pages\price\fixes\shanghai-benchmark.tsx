import { useAtom } from 'jotai'
import { Suspense } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import ShanghaiContentComponent from '~/src/components-metals/Shanghai/ShanghaiContentComponent'
import ShanghaiTitleBlock from '~/src/components-metals/ShanghaiTitleBlock/ShanghaiTitleBlock'
import LatestNewsCell from '~/src/components-news/LatestNewsCell/LatestNewsCell'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import Layout from '~/src/components/Layout/Layout'
import PageLayoutTwoColumns from '~/src/components/PageLayoutTwoColumns/PageLayoutTwoColumns'
import { Query } from '~/src/components/Query/Query'
import { yearReadAtom } from '~/src/components/year-select/year-select.component'
import { metals } from '~/src/lib/metals-factory.lib'
import { refetchInterval } from '~/src/utils/timestamps'

const ShanghaiFix = () => {
  const [read] = useAtom(yearReadAtom)

  const fetcher = metals.shanghaiFixByYear({
    variables: { year: read.all[read.selectedKey].humanReadable },
    options: { refetchInterval },
  })

  return (
    <Layout title="Shanghai Gold Exchange">
      <PageLayoutTwoColumns>
        <main>
          <ShanghaiTitleBlock />
          <ErrBoundary>
            <Suspense fallback={<div>Loading...</div>}>
              <Query fetcher={fetcher}>
                {(res) => (
                  <ShanghaiContentComponent
                    data={res.data}
                    refetch={res.refetch}
                    isFetching={res.isFetching}
                    read={read}
                  />
                )}
              </Query>
            </Suspense>
          </ErrBoundary>
        </main>
        <aside className="rightColumn">
          <AdvertisingSlot
            id={'right-rail-1'}
            className="mx-auto mb-10 h-[250px] w-[300px] desktop:mb-4 no-print"
          />
          <LatestNewsCell />
        </aside>
      </PageLayoutTwoColumns>
    </Layout>
  )
}

export default ShanghaiFix
