import type { NextApiRequest, NextApiResponse } from 'next'
import { getUsername } from '~/src/services/firebase/admin/database'
import { getUserByEmail } from '~/src/services/firebase/admin/service'

/**
 * Checks if the email has a username in the database.
 *
 * @param req : NextApiRequest The request object
 * @param res : NextApiResponse The response object
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' })
  }

  const { email } = req.body

  if (!email) {
    return res.status(400).json({ message: 'Email is required' })
  }

  try {
    const user = await getUserByEmail(email)

    if (!user) {
      return res.status(404).json({ message: 'User not found' })
    }

    const userData = await getUsername(user)

    if (!userData?.username) {
      return res.status(404).json({ message: 'User does not have a username' })
    }

    res.status(200).json({
      username: userData?.username,
      displayUsername: userData?.displayUsername ?? userData?.username,
    })
  } catch (error) {
    console.error('Server error:', error)
    return res.status(500).json({ message: 'Server error' })
  }
}
