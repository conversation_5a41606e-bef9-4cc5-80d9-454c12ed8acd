import type { GetServerSideProps } from 'next'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { type FC, useCallback } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { TeaserCard } from '~/src/components-news/ArticleTeasers/TeaserCard'
import { TeaserTextOnly } from '~/src/components-news/ArticleTeasers/TeaserTextOnly'
import { TeaserWide } from '~/src/components-news/ArticleTeasers/TeaserWide'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { GenericNewsList } from '~/src/components/generic-news-list/generic-news-list.component'
import { NewsCategoryTitle } from '~/src/components/news-category/news-category.component'
import { Spacer } from '~/src/components/spacer/spacer.component'
import type { NewsByCategoryGenericQuery } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import type { TeasersUnion } from '~/src/types/types'
import { useInfinite, useParams } from '~/src/utils/infiniteScroll'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import StrippedString from '~/src/utils/strippedString'
import { urlSafePath } from '~/src/utils/url-safe-path'
import useScreenSize from '~/src/utils/useScreenSize'

export const getServerSideProps: GetServerSideProps = async ({
  res,
  query,
}) => {
  const parentCategory = urlSafePath(query?.parent as string)
  const subCategory = urlSafePath(query?.subcategory as string)

  const variables = {
    urlAlias: `/news/category/${parentCategory}/${subCategory}`,
    limit: 40,
    offset: 0,
    includeRelatedCategories: true,
  }

  // NOTE: TODO: wtf???
  // if you watch the fetch for this query in console.log,
  // you will see that for the initial fetch, subcategory is undefined
  // is next calling the index page at parent before the subcategory page?
  const { dehydratedState } = await ssrQueries({
    ctxRes: res,
    queries: [
      news.newsByCategoryGeneric({ variables }),
      news.newsCategoriesTree(),
    ],
  })

  return {
    props: {
      dehydratedState,
      variables,
    },
  }
}

const SubCategoryPage: FC<{
  variables: any
}> = ({ variables }) => {
  const r = useRouter()
  const subCategoryName = (r.query.subcategory as string).toUpperCase()
  const { params, incrementParams } = useParams(40)
  const { data } = kitcoQuery(
    news.newsByCategoryGeneric({
      variables: { ...params, urlAlias: variables.urlAlias },
      options: {
        enabled: true,
        select: useCallback((d: NewsByCategoryGenericQuery) => {
          const short = d?.nodeListByCategory
          return {
            nodeListByCategory: {
              ...short,
              items: short?.items?.filter(
                (x) =>
                  x?.__typename !== 'Commentary' && Object.keys(x).length !== 0,
              ),
            },
          }
        }, []),
      },
    }),
  )

  const { ref, items, loading } = useInfinite({
    items: data?.nodeListByCategory?.items,
    incrementParams,
    total: data?.nodeListByCategory?.total,
  })

  let itemsOrData = !items?.length ? data?.nodeListByCategory?.items : items
  // Filter and handle teaserSnippet field missing
  itemsOrData = itemsOrData?.map((item) => {
    if (!item.teaserSnippet) {
      const pFirst = StrippedString(item?.source?.description)?.replace(
        '&nbsp;',
        ' ',
      )
      return { ...item, teaserSnippet: pFirst }
    }
    return { ...item }
  })

  const { isMobile } = useScreenSize()

  return (
    <LayoutNewsLanding title={`${subCategoryName} News | KITCO`}>
      <Head>
        <meta
          name="description"
          content="KITCO Covers The Latest Gold News, Silver News, Live Gold Prices, Silver Prices, Gold Charts, Gold Rate, Mining News, ETF, FOREX, Bitcoin, Crypto, Stock Markets"
        />
      </Head>
      <div className="mx-auto box-border w-full max-w-full px-5 md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <NewsCategoryTitle />
        {!isMobile ? (
          <FirstSectionDesktop data={itemsOrData?.slice(0, 4)} />
        ) : (
          <>
            <Spacer className="h-2.5 border-b border-ktc-borders" />
            <FirstSectionMobile data={itemsOrData?.slice(0, 4)} />
          </>
        )}
        <AdvertisingSlot
          id={'banner-2'}
          className={
            'mx-auto my-12 h-[280px] w-[336px] md:h-[90px] md:w-[728px] no-print'
          }
        />
        <Spacer className="h-5" />
        <GenericNewsList
          data={itemsOrData?.slice(4, itemsOrData?.length)}
          hideCategory={true}
          layoutSecond={true}
        />
        <div ref={ref}>{loading && <div>Loading...</div>}</div>
      </div>
      <AdvertisingSlot
        id={'footer'}
        className="after:font-['Font Awesome 5 Pro]' fixed bottom-0 left-1/2
                  z-20
                  w-[320px]
                  -translate-x-1/2 after:absolute after:right-0 after:top-[-25px] after:w-[20px] after:cursor-pointer after:rounded after:bg-[#373737] after:text-center after:text-lg after:font-bold after:leading-[1.1] after:text-white after:content-['X'] tablet:h-[90px] tablet:w-[728px] desktop:hidden no-print"
      />
    </LayoutNewsLanding>
  )
}

export default SubCategoryPage

type Data = NewsByCategoryGenericQuery['nodeListByCategory']['items']

const FirstSectionDesktop: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="flex flex-col border-b border-ktc-borders pb-10 md:pb-0 lg:flex-row lg:pb-10">
      <div className="w-full border-0 border-ktc-borders md:border-b md:pb-[40px] lg:w-[53.4%] lg:border-0 lg:pb-0 lg:pr-[40px]">
        <TeaserCard
          node={data?.[0] as TeasersUnion}
          size="lg"
          sizeImg="lg"
          hideCategory={true}
        />
      </div>
      <div className="mt-10 flex w-full flex-col border-l-ktc-borders pl-0 lg:mt-0 lg:w-[calc(100%_-_53.4%_+_40px)] lg:border-l lg:pl-[40px]">
        <div className="block">
          {data
            ?.slice(1, data?.length)
            .map((node: TeasersUnion, idx: number) => (
              <div className="mb-10" key={node.id ?? idx}>
                <TeaserTextOnly
                  node={node}
                  key={node.id}
                  size="md"
                  hideCategory={true}
                />
              </div>
            ))}
        </div>
      </div>
    </div>
  )
}

const FirstSectionMobile: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="flex flex-col">
      <div className="flex w-full flex-col border-l-ktc-borders pl-0 lg:mt-0 lg:w-1/2 lg:border-l lg:pl-[40px]">
        <div className="block">
          {data
            ?.slice(0, data?.length)
            .map((node: TeasersUnion, idx: number) => (
              <div
                className="border-b border-ktc-borders pt-5"
                key={node.id ?? idx}
              >
                <TeaserWide
                  node={node}
                  size="md"
                  aspectRatio="16x9"
                  key={node.id}
                  hideCategory={true}
                />
              </div>
            ))}
        </div>
      </div>
    </div>
  )
}
