import type { NextApiRequest, NextApiResponse } from 'next'

export default async function vcmsProxy(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'method not allowed' })
  }

  const trimmedPath = req.url?.replace('/api/vcms', '')
  const url = `https://video-api.prod.kitco.com${trimmedPath}`

  const response = await fetch(url, {
    method: 'GET',
    credentials: 'include',
    headers: {
      'Access-Control-Allow-Origin': '*',
      'x-api-key': '7C819B447ABE776E2622D352B148A',
    },
  })

  if (response.status !== 200) {
    console.log('response.status', response.status)
    return res.status(response.status).json({ error: 'error' })
  }

  const data = await response.json()

  res.status(response.status).json(data)
}
