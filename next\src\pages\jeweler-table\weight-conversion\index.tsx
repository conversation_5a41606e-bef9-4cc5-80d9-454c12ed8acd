import type { GetServerSideProps } from 'next'
import type { FC } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { TeaserTextOnly } from '~/src/components-news/ArticleTeasers/TeaserTextOnly'
import DwtOzGramConverter from '~/src/components/DwtOzGramConverter/DwtOzGramConverter'
import DwtOzGramReferenceTable from '~/src/components/DwtOzGramReferenceTable/DwtOzGramReferenceTable'
import { JewelryTitle } from '~/src/components/JewelryTitle/JewelryTitle.component'
import LayoutJewelers from '~/src/components/LayoutJewelers/LayoutJewelers'
import type { ArticleTeaserFragmentFragment } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import cs from '~/src/utils/cs'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import styles from './weight-conversion.module.scss'

export const getServerSideProps: GetServerSideProps = async (c) => {
  const { dehydratedState } = await ssrQueries({
    ctxRes: c?.res,
    queries: [
      news.nodeListQueue({
        variables: { limit: 5, offset: 0, queueId: 'latest_news' },
      }),
    ],
  })

  return {
    props: {
      dehydratedState,
    },
  }
}

const WeightConversion = () => {
  return (
    <LayoutJewelers title="Jeweler Resources - DWT/Oz/Gram Conversion - Kitco">
      <div className="mx-auto box-border w-full max-w-full px-5 md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <JewelryTitle />
        <div
          className={cs([
            styles.container,
            'mb-20 mt-2.5 gap-[20px] md:mb-[120px] md:mt-4',
          ])}
        >
          <main className={styles.main}>
            <section className={cs([styles.block, styles.converter])}>
              <DwtOzGramConverter />
            </section>
            <h3 className={styles.tableTitle}>Reference table</h3>
            <div className={cs(['overflow-y-scroll', styles.wrapper])}>
              <section
                className={cs([styles.block, styles.referenceTable, 'gap-3'])}
              >
                <DwtOzGramReferenceTable from={10} to={330} />
                <DwtOzGramReferenceTable from={340} to={660} />
              </section>
            </div>
          </main>
          <aside className="hidden w-[300px] lg:col-span-1 lg:block">
            <div className="px-2.5">
              <AdvertisingSlot
                id={'right-rail-1'}
                className="mx-auto mb-4 hidden h-[250px] w-[300px] lg:block no-print"
              />
              <LatestNewsSection />
              <AdvertisingSlot
                id={'right-rail-2'}
                className="mx-auto mt-4 hidden h-[600px] w-[300px] lg:block no-print"
              />
            </div>
          </aside>
        </div>
      </div>
    </LayoutJewelers>
  )
}

export default WeightConversion

const LatestNewsSection: FC = () => {
  const { data } = kitcoQuery(
    news.nodeListQueue({
      variables: { limit: 5, offset: 0, queueId: 'latest_news' },
    }),
  )
  return (
    <div className="flex flex-col">
      <h2 className="border-b border-ktc-borders pb-2.5 text-[20px] uppercase">
        <span>Latest News</span>
      </h2>
      <div className="flex flex-grow flex-col">
        {data?.queue?.items
          ?.slice(0, 5)
          .map((x: ArticleTeaserFragmentFragment) => {
            return (
              <div className="mt-5 flex" key={x.id}>
                <TeaserTextOnly
                  key={x?.id}
                  node={x}
                  hideSummary={true}
                  size={'sm'}
                />
              </div>
            )
          })}
      </div>
    </div>
  )
}
