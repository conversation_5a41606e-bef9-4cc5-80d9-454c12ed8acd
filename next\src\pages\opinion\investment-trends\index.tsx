import type { GetServerSideProps } from 'next'
import dynamic from 'next/dynamic'
import type { FC } from 'react'
import { TeaserTextOnly } from '~/src/components-news/ArticleTeasers/TeaserTextOnly'
import { TeaserWide } from '~/src/components-news/ArticleTeasers/TeaserWide'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { GenericNewsList } from '~/src/components/generic-news-list/generic-news-list.component'
import { NewsCategoryTitle } from '~/src/components/news-category/news-category.component'
import { Spacer } from '~/src/components/spacer/spacer.component'
import type { Sponsored, SponsoredContentQuery } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import type { TeasersUnion } from '~/src/types/types'
import { useInfinite, useParams } from '~/src/utils/infiniteScroll'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import useScreenSize from '~/src/utils/useScreenSize'

const TeaserCard = dynamic(
  async () =>
    await import('~/src/components-news/ArticleTeasers/TeaserCard').then(
      (mod) => mod.TeaserCard,
    ),
  { ssr: false },
)
export { TeaserCard }

export const getServerSideProps: GetServerSideProps<any> = async (c) => {
  const { dehydratedState } = await ssrQueries({
    ctxRes: c.res,
    queries: [
      news.sponsoredContent({ variables: { limit: 10, offset: 0 } }),
      news.newsCategoriesTree(),
    ],
  })
  return {
    props: { dehydratedState },
  }
}

const OpinionsInvestmentTrends: FC<any> = () => {
  const { params, incrementParams } = useParams(10)
  const { data } = kitcoQuery(
    news.sponsoredContent({
      variables: { ...params },
      options: { enabled: true },
    }),
  )

  const { ref, items, loading } = useInfinite({
    items: data?.nodeList?.items as Sponsored[],
    incrementParams,
    total: data?.nodeList?.total,
  })

  const dataMap =
    items?.map((item: any) => {
      return {
        ...item,
        teaserSnippet:
          item?.teaserSnippet ?? item?.bodyWithEmbeddedMedia?.value,
      }
    }) ?? []

  const { isMobile } = useScreenSize()

  return (
    <LayoutNewsLanding title="Investment Trends">
      <div className="mx-auto box-border w-full max-w-full px-[20px] md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <NewsCategoryTitle />
        <FirstSection isMobile={isMobile} data={dataMap?.slice(0, 4)} />
        <Spacer className="h-5" />
        <GenericNewsList
          data={dataMap?.slice(4)}
          hideCategory={true}
          layoutSecond={true}
        />
        <div ref={ref}>{loading && <div>Loading...</div>}</div>
      </div>
    </LayoutNewsLanding>
  )
}

export default OpinionsInvestmentTrends

type Data = SponsoredContentQuery['nodeList']['items']

const FirstSection: FC<{ isMobile: boolean; data: Data }> = ({
  isMobile,
  data,
}) => {
  if (!isMobile) return <FirstSectionDesktop data={data} />

  return (
    <>
      <Spacer className="h-2.5 border-b border-ktc-borders" />
      <FirstSectionMobile data={data} />
    </>
  )
}

const FirstSectionDesktop: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="flex flex-col border-b border-ktc-borders pb-10 lg:flex-row">
      <div className="w-full border-0 border-ktc-borders md:border-b md:pb-[40px] lg:w-[53.4%] lg:border-0 lg:pb-0 lg:pr-[40px]">
        <TeaserCard
          node={data?.[0] as TeasersUnion}
          size="xl"
          sizeImg="xl"
          hideCategory={true}
        />
      </div>
      <div className="mt-10 flex w-full flex-col border-l-ktc-borders pl-0 lg:mt-0 lg:w-[calc(100%_-_53.4%_+_40px)] lg:border-l lg:pl-[40px]">
        <div className="block">
          {data
            ?.slice(1, data?.length)
            .map((node: TeasersUnion, idx: number) => (
              <div className="mb-10" key={node.id ?? idx}>
                <TeaserTextOnly
                  node={node}
                  key={node.id}
                  size="md"
                  hideCategory={true}
                />
              </div>
            ))}
        </div>
      </div>
    </div>
  )
}

const FirstSectionMobile: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="flex flex-col">
      <div className="flex w-full flex-col border-l-ktc-borders pl-0 lg:mt-0 lg:w-1/2 lg:border-l lg:pl-[40px]">
        <div className="block">
          {data
            ?.slice(0, data?.length)
            .map((node: TeasersUnion, idx: number) => (
              <div
                className="border-b border-ktc-borders pt-5"
                key={node.id ?? idx}
              >
                <TeaserWide
                  node={node}
                  size="md"
                  aspectRatio="16x9"
                  key={node.id}
                  hideCategory={true}
                />
              </div>
            ))}
        </div>
      </div>
      {/*<AdvertisingSlot*/}
      {/*  id={`banner-0`}*/}
      {/*  className={"h-[250px] w-[300px] bg-red-400 mx-auto my-5"}*/}
      {/*  viewportsEnabled={{*/}
      {/*    mobile: true,*/}
      {/*    tablet: false,*/}
      {/*    desktop: false,*/}
      {/*  }}*/}
      {/*/>*/}
    </div>
  )
}
