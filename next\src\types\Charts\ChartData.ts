/**
 * ChartData interface
 *
 * This interface defines the structure of data for a chart.
 * It includes an array of UNIX timestamps and an array of corresponding values.
 *
 * @interface ChartData
 *
 * @property {number[]} labels - Array of UNIX timestamps
 * @property {number[]} values - Array of corresponding values (e.g., prices)
 */
type ChartData = {
  labels: number[] | string[]
  values: number[]
}

export default ChartData
