import type { GetServerSideProps, NextPage } from 'next'
import { Suspense, useEffect, useState } from 'react'
import { ErrorBoundary } from 'react-error-boundary'
import { ContentWrapper } from '~/src/components-news/ContentWrapper/ContentWrapper.component'
import { NewsVideosPagesCategoryAndDetailHeader } from '~/src/components-news/NewsPagesHeaders/NewsVideosPagesHeaders.component'
import { Playlist } from '~/src/components-news/Playlist/Playlist.component'
import {
  FeaturedVideo,
  Section,
  UpNext,
} from '~/src/components-news/VideoPagesTopSection/VideoPagesTopSection.component'
import { VideoPlaylistTeaser } from '~/src/components-news/VideoPlaylistTeaser/VideoPlaylistTeaser'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { Query } from '~/src/components/Query/Query'
import { vcms } from '~/src/lib/vcms-factory.lib'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import { Loaders } from '..'

export const getServerSideProps: GetServerSideProps = async (c) => {
  if (!c.params?.alias || !c.params?.date) {
    return { notFound: true }
  }

  const urlAlias = `/news/video/${c.params.date}/${c.params.alias}`
  const { dehydratedState } = await ssrQueries({
    ctxRes: c.res,
    queries: [vcms.categories()],
  })

  c.res.setHeader('Cache-Control', 's-maxage=3600, stale-while-revalidate')
  return {
    props: {
      dehydratedState,
      urlAlias,
    },
  }
}

const NewsVideoAliasPage: NextPage<{ urlAlias: string }> = ({ urlAlias }) => {
  const [currentCategory, setCurrentCategory] = useState(null)

  const getNode = vcms.snippetBySlug({
    variables: { slug: urlAlias },
    options: {
      enabled: Boolean(urlAlias),
    },
  })

  useEffect(() => {
    const newData: any = getNode

    if (newData?.VideoConsumerSnippetBySlug?.category) {
      setCurrentCategory(newData.VideoConsumerSnippetBySlug.category)
    }
  }, [getNode])

  const feeds = vcms.feed({
    variables: {
      upNext: true,
      latest: true,
    },
    options: {
      enabled: true,
    },
  })

  const categoryVideos = vcms.categoryById({
    variables: { id: currentCategory?.id },
    options: { enabled: !!currentCategory?.id },
  })

  return (
    <LayoutNewsLanding title="Videos" enableDarkBG={true}>
      <div className="bg-[#192732] px-0 pt-[20px] text-white lg:px-8 lg:pt-[50px]">
        <ContentWrapper>
          <ErrorBoundary fallback={<p>Error loading</p>}>
            <Suspense
              fallback={
                <div className="animate-loading aspect-video h-full w-full bg-white/10" />
              }
            >
              <Query fetcher={getNode}>
                {(res) => (
                  <NewsVideosPagesCategoryAndDetailHeader
                    routeLabel={
                      res?.data?.VideoConsumerSnippetBySlug?.categories[0]?.name
                    }
                  />
                )}
              </Query>
            </Suspense>
          </ErrorBoundary>
        </ContentWrapper>
      </div>
      <Section>
        <ErrorBoundary fallback={<p>Error loading</p>}>
          <Suspense
            fallback={
              <div className="animate-loading aspect-video h-full w-full bg-white/10" />
            }
          >
            <Query fetcher={getNode}>
              {(res) => (
                <FeaturedVideo
                  node={res?.data?.VideoConsumerSnippetBySlug}
                  isFetching={res.isFetching || res.isLoading}
                />
              )}
            </Query>
            <Query fetcher={feeds}>
              {(res) => (
                <UpNext
                  nodes={res?.data?.VideoConsumerFeed?.upNext?.slice(0, 4)}
                  isFetching={res.isFetching}
                />
              )}
            </Query>
          </Suspense>
        </ErrorBoundary>
      </Section>
      <div className="px-0 lg:px-8">
        <ContentWrapper className="border-b border-b-white/10 pb-10">
          <Playlist.Title>
            MORE {currentCategory?.name?.toUpperCase()}
          </Playlist.Title>
          <Playlist.Row>
            <ErrorBoundary fallback={<p>Error loading</p>}>
              <Suspense
                fallback={
                  <div className="animate-loading aspect-video h-full w-full bg-white/10" />
                }
              >
                <Query fetcher={categoryVideos}>
                  {(res) =>
                    res?.data?.VideoConsumerCategoryById?.edges?.snippets
                      ?.slice(1, 26)
                      .map((x) => (
                        <VideoPlaylistTeaser
                          isFetching={res.isFetching || res.isLoading}
                          node={x}
                          key={x.id}
                        />
                      ))
                  }
                </Query>
              </Suspense>
            </ErrorBoundary>
          </Playlist.Row>
        </ContentWrapper>
      </div>

      <div className="px-0 lg:px-8">
        <Query fetcher={feeds}>
          {(res) => (
            <ContentWrapper className="pb-10">
              <Playlist.Title>Latest Videos</Playlist.Title>
              <>
                <Playlist.Row>
                  {res.isFetching ? (
                    <Loaders />
                  ) : (
                    res?.data?.VideoConsumerFeed?.latest
                      ?.slice(1, 5)
                      ?.map((x) => <VideoPlaylistTeaser node={x} key={x.id} />)
                  )}
                </Playlist.Row>
                {!res?.data?.VideoConsumerFeed?.latest?.length && (
                  <p className="text-2xl text-white">
                    That&apos;s all the videos we have for now. Please check
                    back later.
                  </p>
                )}
              </>
            </ContentWrapper>
          )}
        </Query>
      </div>
    </LayoutNewsLanding>
  )
}

export default NewsVideoAliasPage
