import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import timestampRange from '~/src/utils/DateTime/timestampRange'

// Extend dayjs to use timezone and UTC plugins
dayjs.extend(utc)
dayjs.extend(timezone)

/**
 * Creates a time range between the start and end date with the specified interval.
 *
 * @param {Dayjs} startDate - The start date
 * @param {Dayjs} endDate - The end date
 * @param {number} interval - The interval in minutes
 * @returns {number[]} - The time range as an array of UNIX timestamps
 */
function createTimeRange(
  startDate: dayjs.Dayjs,
  endDate: dayjs.Dayjs,
  interval: number,
): number[] {
  const timezoneName = process.env.NEXT_PUBLIC_TIMEZONE || 'America/New_York'

  // Ensure that both startDate and endDate are in the correct timezone
  const adjustedStartDate = startDate.tz(timezoneName)
  const adjustedEndDate = endDate.tz(timezoneName)

  // Generate timestamps using the timestampRange utility function
  return timestampRange(adjustedStartDate, adjustedEndDate, interval)
}

export default createTimeRange
