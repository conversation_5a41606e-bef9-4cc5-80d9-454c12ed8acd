import type { MarketStatusQuery } from '~/src/generated'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { markets } from '../lib/markets-factory.lib'

/**
 * Convert the next status to a human-readable format
 *
 * @param next
 */
function convertNextStatusToFormat(next: number | undefined) {
  const h = Math.floor(next / 3600)
  const m = (next - h * 3600) / 60

  const hDisplay = h > 0 ? h + (h === 1 ? ' hr. ' : ' hrs. ') : ''
  const mDisplay = m > 0 ? m + (m === 1 ? ' min. ' : ' mins. ') : ''
  return hDisplay + mDisplay
}

/**
 * Interface for useGetMarketStatusReturn return type
 *
 * @interface useGetMarketStatusReturn
 * @property {MarketStatusQuery} data - The market status data
 * @property {string} statusString - The formatted status string
 * @property {string} timeToNextStatusString - The formatted time to next status string
 */
interface useGetMarketStatusReturn {
  data: MarketStatusQuery
  statusString: string
  timeToNextStatusString: string
}

/**
 * Get the market status
 */
export function useGetMarketStatus(): useGetMarketStatusReturn {
  const { data } = kitcoQuery(
    markets.marketStatus({ options: { refetchOnWindowFocus: true } }),
  )

  const gst = data?.GetMarketStatus
  const statusString = `Market is ${gst?.status ?? '-'}`
  const invert = gst?.status === 'OPEN' ? 'CLOSE' : 'OPEN'
  const timeToNextStatusString = `Will ${invert} in ${convertNextStatusToFormat(
    gst?.next ?? 0,
  )}`

  return {
    data,
    statusString,
    timeToNextStatusString,
  }
}

/**
 * Extended Market Status Interface
 *
 * This interface extends the MarketStatusQuery interface with additional properties for the current market status string and formatted time to the next status change.
 *
 * @interface ExtendedMarketStatus
 * @extends {MarketStatusQuery}
 * @property {string} statusString - The current market status string.
 * @property {string} timeToNextStatusString - The formatted time to the next status change.
 */
export interface ExtendedMarketStatus extends MarketStatusQuery {
  statusString: string
  timeToNextStatusString: string
}

/**
 * Get the formatted market status and time to the next status
 *
 * This function fetches the current market status for SSR and returns formatted strings for the current status
 * and the time remaining until the market changes status.
 *
 * @async
 * @function ssrGetMarketStatus
 * @returns {Promise<ExtendedMarketStatus>} - An object containing the raw data, current market status string, and formatted time to the next status change.
 */
export async function ssrGetMarketStatus(): Promise<ExtendedMarketStatus> {
  // Fetch the market status data
  // @ts-ignore
  const data: MarketStatusQuery = await markets.marketStatus().queryFn()

  // Destructure the market status data
  const gst = data?.GetMarketStatus

  // Create a string representing the current market status
  const statusString = `Market is ${gst?.status ?? '-'}`

  // Determine the next market status (OPEN or CLOSE) based on the current status
  const invert = gst?.status === 'OPEN' ? 'CLOSE' : 'OPEN'

  // Create a formatted string indicating when the market will change status
  const timeToNextStatusString = `Will ${invert} in ${convertNextStatusToFormat(
    gst?.next ?? 0,
  )}`

  return {
    data,
    statusString,
    timeToNextStatusString,
  } as ExtendedMarketStatus
}
