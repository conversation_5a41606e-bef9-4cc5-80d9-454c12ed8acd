import type { CurrenciesQuery } from '~/src/generated'

/**
 * Converts from USD to the target currency.
 *
 * @param {string} symbol - The symbol of the target currency
 * @param {CurrenciesQuery} currencies - The currency data
 * @returns {number} - The conversion rate from USD to the target currency
 */
export const conversionRateFromUSD = (
  symbol: string,
  currencies: CurrenciesQuery,
): number => {
  // If symbol or currencies are undefined, return 1
  if (!symbol || !currencies) return 1

  // If the target currency is USD, no conversion is needed
  if (symbol === 'USD') return 1

  // Get the conversion rate from USD to the target currency
  const rateUsdToTarget = currencies?.[symbol]?.results?.[0]?.ctousd
  if (!rateUsdToTarget) return 1

  // Return the conversion rate from USD to the target currency
  return rateUsdToTarget
}

export default conversionRateFromUSD
