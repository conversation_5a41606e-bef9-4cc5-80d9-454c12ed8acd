import clsx from 'clsx'
import type { GetServerSideProps, NextPage } from 'next'
import Head from 'next/head'
import Link from 'next/link'
import { Suspense } from 'react'
import { ContentWrapper } from '~/src/components-news/ContentWrapper/ContentWrapper.component'
import { NewsVideosPagesHeader } from '~/src/components-news/NewsPagesHeaders/NewsVideosPagesHeaders.component'
import { Playlist } from '~/src/components-news/Playlist/Playlist.component'
import { VideoCategoryRow } from '~/src/components-news/VideoCategoryRow/VideoCategoryRow'
import {
  FeaturedVideo,
  Section,
  UpNext,
} from '~/src/components-news/VideoPagesTopSection/VideoPagesTopSection.component'
import { VideoPlaylistTeaser } from '~/src/components-news/VideoPlaylistTeaser/VideoPlaylistTeaser'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { Query } from '~/src/components/Query/Query'
import { vcms } from '~/src/lib/vcms-factory.lib'
import { ssrQueries } from '~/src/utils/ssr-wrappers'

export const getServerSideProps: GetServerSideProps = async (c) => {
  const { dehydratedState } = await ssrQueries({
    ctxRes: c.res,
    queries: [vcms.categories()],
  })

  return {
    props: {
      dehydratedState,
    },
  }
}

const VideosLandingPage: NextPage = () => {
  const feeds = vcms.feed({
    variables: {
      latest: true,
      upNext: true,
    },
    options: { enabled: true },
  })

  return (
    <LayoutNewsLanding
      title="Gold, Silver, Gold Video, Silver Video, Gold News, Kitco Video News | KITCO VIDEO"
      enableDarkBG={true}
    >
      <Head>
        <meta
          name="description"
          content="Get the latest in market news, gold news, silver news and mining coverage from Kitco News' team of veteran journalists and industry experts."
        />
      </Head>
      <div className="bg-[#192732] px-0 pt-[20px] text-white lg:px-8 lg:pt-[50px]">
        <ContentWrapper>
          <NewsVideosPagesHeader />
        </ContentWrapper>
      </div>
      <Section>
        <ErrBoundary>
          <Suspense fallback={<Loaders />}>
            <Query fetcher={feeds}>
              {(res) => {
                return (
                  <>
                    <FeaturedVideo
                      node={res?.data?.VideoConsumerFeed?.latest?.[0]}
                      isFetching={res?.isFetching || res?.isLoading}
                    />
                    <UpNext
                      nodes={res?.data?.VideoConsumerFeed?.latest?.slice(1, 5)}
                      isFetching={res.isFetching || res.isLoading}
                    />
                  </>
                )
              }}
            </Query>
          </Suspense>
        </ErrBoundary>
      </Section>
      <div className="px-0 lg:px-8">
        <Query fetcher={feeds}>
          {(res) => (
            <ContentWrapper className="border-b border-b-white/10 pb-10">
              <Playlist.Title>Latest Videos</Playlist.Title>
              <>
                <Playlist.Row>
                  {res.isFetching ? (
                    <Loaders />
                  ) : (
                    res?.data?.VideoConsumerFeed?.latest
                      ?.slice(5, 9)
                      ?.map((x) => <VideoPlaylistTeaser node={x} key={x.id} />)
                  )}
                </Playlist.Row>
                {!res?.data?.VideoConsumerFeed?.latest?.length && (
                  <p className="text-2xl text-white">
                    That&apos;s all the videos we have for now. Please check
                    back later.
                  </p>
                )}
              </>
            </ContentWrapper>
          )}
        </Query>
      </div>
      <ErrBoundary>
        <Suspense fallback={<Loaders />}>
          <Query fetcher={vcms.categories()}>
            {(res) => (
              <div className="px-0 lg:px-8">
                {res?.data?.VideoConsumerCategories?.map((category, idx) => (
                  <ContentWrapper
                    key={category?.id}
                    className={clsx(
                      'pb-10',
                      idx !== res?.data?.VideoConsumerCategories?.length - 1
                        ? 'border-b border-b-white/10'
                        : null,
                    )}
                  >
                    <Playlist.Title>
                      <Link
                        href={`/news/video${category?.urlAlias}`}
                        className="text-white"
                      >
                        {category.name}
                      </Link>
                    </Playlist.Title>
                    <VideoCategoryRow id={Number(category.id)} />
                  </ContentWrapper>
                ))}
              </div>
            )}
          </Query>
        </Suspense>
      </ErrBoundary>
    </LayoutNewsLanding>
  )
}

export default VideosLandingPage

export const Loaders = () => (
  <>
    {Array.from(Array(8).keys()).map((_, idx) => (
      <Playlist.TeaserItemLoading key={idx} />
    ))}
  </>
)
