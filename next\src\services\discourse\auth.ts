import type { User } from 'firebase/auth'
import type { UserData } from '~/src/components/Auth/Types/UserData'

/**
 * This function generates the headers required for Discourse API requests.
 */
export const authHeaders = () => {
  // Encode the basic auth credentials
  const auth = Buffer.from(
    `${process.env.DISCOURSE_BASIC_AUTH_USER}:${process.env.DISCOURSE_BASIC_AUTH_PASS}`,
  ).toString('base64')

  return {
    Authorization: `Basic ${auth}`,
    'Api-Key': process.env.DISCOURSE_API_KEY,
    'Api-Username': process.env.DISCOURSE_API_USERNAME,
  }
}

/**
 * This function generates a Discourse SSO URL for the user to log in.
 * Throws an error if the URL fails to generate.
 *
 * @param user
 * @param userData
 */
export const generateDiscourseSSOURL = async (
  user: User,
  userData: UserData,
) => {
  // Fetch the SSO URL from the server
  const ssoUrlResponse = await fetch('/api/discourse/getSSOURL', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email: user.email,
      username: userData.username,
      name: user.displayName,
      external_id: user.uid,
    }),
  })

  if (!ssoUrlResponse.ok) {
    return false
  }

  const { url: verifyURL } = await ssoUrlResponse.json()

  return verifyURL
}
