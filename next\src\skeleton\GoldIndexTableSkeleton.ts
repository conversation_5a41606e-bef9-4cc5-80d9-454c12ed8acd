import type { GoldIndexTableQuery, GoldIndexWidgetQuery } from '~/src/generated'

// Create a skeleton entry for a commodity
const createSkeletonEntry = (commodity: string) => {
  return {
    [commodity]: {
      results: [
        {
          bid: 0.0,
          change: 0.0,
          changePercentage: 0.0,
          extra:
            '{"ChangePercentTrade":0,"ChangePercentUSD":0,"ChangeTrade":0,"ChangeUSD":0}',
          currency: 'USD',
          originalTime: '2020-01-01 00:00:00',
        },
      ],
    },
  }
}

const preciousMetals = ['Gold', 'Silver', 'Platinum', 'Palladium']

const allCommodities = [
  'Gold',
  'Silver',
  'Platinum',
  'Palladium',
  'Copper',
  'Nickel',
  'Aluminum',
  'Zinc',
  'Lead',
]

/**
 * Generate the skeleton table data
 *
 * @param {boolean} onlyPreciousMetals - Whether to get only precious metals
 * @constructor
 */
const GoldIndexTableSkeleton = (
  onlyPreciousMetals?: boolean,
): GoldIndexTableQuery | GoldIndexWidgetQuery => {
  const commodities = onlyPreciousMetals ? preciousMetals : allCommodities

  return commodities.reduce((acc, commodity) => {
    return Object.assign(acc, createSkeletonEntry(commodity))
  }, {}) as GoldIndexTableQuery | GoldIndexWidgetQuery
}

export default GoldIndexTableSkeleton
