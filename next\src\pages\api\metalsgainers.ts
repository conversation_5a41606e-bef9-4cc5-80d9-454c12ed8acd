export const config = {
  runtime: 'edge',
}

const base = 'https://kitco2.websol.barchart.com/?module=screener'

// Helper function to fetch and format data
const fetchAndFormatData = async (preset) => {
  try {
    const response = await fetch(
      `${base}&preset=${preset}&results=5&output=json`,
    )
    if (!response.ok) {
      throw new Error(`Failed to fetch data for: ${preset}`)
    }
    const data: any = await response.json()
    return Object.values(data?.quotes)
  } catch (error) {
    console.error(`Error fetching data for ${preset}:`, error)
    return []
  }
}

// Todo: Change this to GraphQL
export default async function handler(_req) {
  const formedData: any = {}

  // Perform all requests concurrently
  const [gainers, losers, fiftyTwoWkGainers, fiftyTwoWkLosers] =
    await Promise.all([
      fetchAndFormatData('52wkhighMetals'),
      fetchAndFormatData('52wklowMetals'),
      fetchAndFormatData('52wkhighpctMetals'),
      fetchAndFormatData('52wklowpctMetals'),
    ])

  // Assign formatted data
  formedData.gainers = gainers
  formedData.losers = losers
  formedData.fiftytwowkgainers = fiftyTwoWkGainers
  formedData.fiftytwowklosers = fiftyTwoWkLosers

  return new Response(JSON.stringify(formedData), {
    headers: { 'Content-Type': 'application/json' },
  })
}
