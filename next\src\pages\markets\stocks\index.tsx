import { AdvertisingSlot } from 'react-advertising'
import BarchartChartGrid from '~/src/components-markets/BarchartChartGrid/BarchartChartGrid'
import BarchartsLeadersCell from '~/src/components-markets/BarchartsLeadersCell/BarchartsLeadersCell'
import LatestNewsCell from '~/src/components-news/LatestNewsCell/LatestNewsCell'
import Layout from '~/src/components/Layout/Layout'
import { Barcharts } from '~/src/features/bar-charts/barcharts'

const StocksLanding = () => {
  return (
    <Layout title="Stocks">
      <div className="lg:layout-cols-2 sm:layout-cols-1">
        <div className="left">
          <BarchartChartGrid columns={2}>
            <Barcharts
              symbol="$DOWI"
              href="/markets/indices/$DOWI"
              title="Dow Jones Industrial Average "
            />
            <div className="mt-8 block md:mt-0">
              <Barcharts
                symbol="$SPX"
                title="S&P 500"
                href="/markets/indices/$SPX"
              />
            </div>
          </BarchartChartGrid>
          <div className="mb-6 mt-6">
            <BarchartsLeadersCell leaderType="active" />
          </div>
          <div className="mb-6">
            <BarchartsLeadersCell leaderType="gainers" />
          </div>
          <div className="mb-6">
            <BarchartsLeadersCell leaderType="losers" />
          </div>
        </div>
        <div className="right">
          <AdvertisingSlot
            id={'right-rail-1'}
            className="mx-auto mb-4 hidden h-[250px] w-[300px] desktop:flex no-print"
          />
          <LatestNewsCell />
        </div>
      </div>
    </Layout>
  )
}

export default StocksLanding
