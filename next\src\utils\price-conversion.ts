import currency from 'currency.js'
import { pf } from './price-formatter'

// T_OUNCE = troy ounce
// a troy ounce is a measurement for precious metals and equals 31.1034768

const T_OUNCE_TO = {
  GRAM: 31.1035,
  KILO: 0.0311035, // Old value: 32.1507
  PENNYWEIGHT: 20,
  TOLA: 2.6666646776086, // Old value: 2.66666,
  TAEL: 0.822857, // Old value: 1.21528,
} as const

// ratio to pounds
const POUND_TO = {
  GRAM: 0.00220462, // Gram - x 0.00220462 Pounds (lbs)
  KILO: 2.20462, // Kilo - x 2.20462 Pounds (lbs); or x 1,000 Grams (gr)
  TON: 2240, // Ton (Imperial)  - x 2,240 pounds (lbs)
  TONNE: 2204.62, // Tonne (Metric) - x 1,000 kilo (kg); or x 2,204.62 Pounds (lbs)
} as const

const opts = { symbol: '' }

function priceToOz(bid: number): string {
  return currency(bid, opts).format()
}

function priceBMToPound(
  bid: number,
  opts = { symbol: '', precision: 4 },
): string {
  return currency(bid, opts).format()
}

function priceBMToGram(
  bid: number,
  opts = { symbol: '', precision: 4 },
): string {
  return currency(bid * POUND_TO.GRAM, opts).format()
}

function priceBMToKilo(
  bid: number,
  opts = { symbol: '', precision: 4 },
): string {
  return currency(bid * POUND_TO.KILO, opts).format()
}

function priceBMToTon(bid: number): string {
  return currency(bid * POUND_TO.TON, { symbol: '', precision: 2 }).format()
}

function priceBMToTonne(bid: number): string {
  return currency(bid * POUND_TO.TONNE, { symbol: '', precision: 2 }).format()
}

// format the change in price
function formatChange(change: number, precision: number): string {
  const roundedChange = change.toFixed(precision)
  return `${roundedChange}`
}

function priceBMTChangeKilo(bid: number, change: number): string {
  const start = (bid - change) * POUND_TO.KILO
  const now = bid * POUND_TO.KILO

  const answer = now - start
  return formatChange(answer, 3)
}

function priceBMTChangeGram(bid: number, change: number): string {
  const start = (bid - change) * POUND_TO.GRAM
  const now = bid * POUND_TO.GRAM

  const answer = now - start
  return formatChange(answer, 3)
}

function priceBMTChangeTon(bid: number, change: number): string {
  const start = (bid - change) * POUND_TO.TON
  const now = bid * POUND_TO.TON

  const answer = now - start
  return formatChange(answer, 2)
}

function priceBMTChangeTonne(bid: number, change: number): string {
  const start = (bid - change) * POUND_TO.TONNE
  const now = bid * POUND_TO.TONNE

  const answer = now - start
  return formatChange(answer, 2)
}

function priceToGram(bid: number): string {
  return currency(bid, opts).divide(T_OUNCE_TO.GRAM).format()
}

function priceToKilo(bid: number): string {
  return currency(bid, opts).divide(T_OUNCE_TO.KILO).format()
}

function priceToPennyweight(bid: number): string {
  return currency(bid, opts).divide(T_OUNCE_TO.PENNYWEIGHT).format()
}

function priceToTola(bid: number): string {
  return currency(bid, opts).divide(T_OUNCE_TO.TOLA).format()
}

function priceToTael(bid: number): string {
  return currency(bid, opts).divide(T_OUNCE_TO.TAEL).format()
}

function priceChangeKilo(bid: number, change: number): string {
  const start = (bid - change) / T_OUNCE_TO.KILO
  const now = bid / T_OUNCE_TO.KILO

  const answer = now - start
  return pf(answer)
}

function priceChangeGram(bid: number, change: number): string {
  const start = (bid - change) / T_OUNCE_TO.GRAM
  const now = bid / T_OUNCE_TO.GRAM

  const answer = now - start
  return pf(answer)
}

function priceChangePenny(bid: number, change: number): string {
  const start = (bid - change) / T_OUNCE_TO.PENNYWEIGHT
  const now = bid / T_OUNCE_TO.PENNYWEIGHT

  const answer = now - start
  return pf(answer)
}

function priceChangeTola(bid: number, change: number): string {
  const start = (bid - change) / T_OUNCE_TO.TOLA
  const now = bid / T_OUNCE_TO.TOLA
  const answer = now - start
  return pf(answer)
}

function priceChangeTael(bid: number, change: number): string {
  const start = (bid - change) / T_OUNCE_TO.TAEL
  const now = bid / T_OUNCE_TO.TAEL
  const answer = now - start
  return pf(answer)
}

export const convert = {
  priceToOz,
  priceToGram,
  priceToKilo,
  priceToPennyweight,
  priceToTola,
  priceToTael,
  priceChangeKilo,
  priceChangeGram,
  priceChangePenny,
  priceChangeTola,
  priceChangeTael,
  priceBMToPound,
  priceBMToGram,
  priceBMToKilo,
  priceBMToTon,
  priceBMToTonne,
  priceBMTChangeKilo,
  priceBMTChangeGram,
  priceBMTChangeTon,
  priceBMTChangeTonne,
}

export type TConvertPrice = typeof convert
