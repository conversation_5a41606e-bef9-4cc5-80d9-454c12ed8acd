import crypto from 'crypto'
import type { NextApiRequest, NextApiResponse } from 'next'
import { URLSearchParams } from 'url'

interface Payload {
  nonce: string
  email: string
  username: string
  external_id: string
  name: string
}

/**
 * Generates a secure nonce for use in the SSO payload.
 */
function generateNonce() {
  return crypto.randomBytes(16).toString('hex')
}

/**
 * Constructs the SSO URL for Discourse.
 *
 * @param {Object} payload The payload with user information.
 * @param {string} secret The shared secret for the SSO signature.
 * @returns {string} The full URL to redirect the user to Discourse.
 */
function constructSSOUrl(payload: Payload, secret: string): string {
  // @ts-ignore
  const params = new URLSearchParams(payload)
  const base64Payload = Buffer.from(params.toString()).toString('base64')
  const signature = crypto
    .createHmac('sha256', secret)
    .update(base64Payload)
    .digest('hex')

  const returnUrl = `${process.env.NEXT_PUBLIC_DISCOURSE_URL}session/sso_login`
  return `${returnUrl}?sso=${encodeURIComponent(
    base64Payload,
  )}&sig=${encodeURIComponent(signature)}`
}

/**
 * API endpoint to generate a Discourse SSO URL.
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method Not Allowed' })
  }

  // Ensure all required user details are present
  if (
    !req.body.email ||
    !req.body.username ||
    !req.body.name ||
    !req.body.external_id
  ) {
    return res.status(400).json({ error: 'Missing required user information' })
  }

  // Ensure the SSO secret is configured
  const secret = process.env.DISCOURSE_SSO_SECRET
  if (!secret) {
    return res.status(500).json({ error: 'SSO secret not configured' })
  }

  // Generate nonce and prepare payload
  const nonce = generateNonce()
  const payload = {
    nonce: nonce,
    email: req.body.email,
    username: req.body.username,
    external_id: req.body.external_id,
    name: req.body.name,
  }

  // Construct and return the SSO URL
  try {
    const ssoUrl = constructSSOUrl(payload, secret)
    return res.status(200).json({ url: ssoUrl })
  } catch (error) {
    console.error('Failed to generate SSO URL:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
