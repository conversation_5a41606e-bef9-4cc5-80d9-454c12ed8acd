import currency from 'currency.js'

const cur = (val: number) => currency(val, { symbol: '' })
const ONE_MILLION = 1_000_000
const ONE_BILLION = 1_000_000_000
const ONE_TRILLION = 1_000_000_000_000

export function currencyFmt(value: number): string {
  const suffixes = ['', 'M', 'B', 'T']
  const divisors = [1, ONE_MILLION, ONE_BILLION, ONE_TRILLION]

  for (let i = suffixes.length - 1; i >= 0; i--) {
    if (value >= divisors[i]) {
      return `${cur(value).divide(divisors[i]).format()}${suffixes[i]}`
    }
  }

  return cur(value).format()
}
