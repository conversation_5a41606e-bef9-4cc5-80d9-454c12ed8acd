import type { CurrenciesQuery } from '~/src/generated'
import conversionRateFromUSD from '~/src/utils/Conversion/conversionRateFromUSD'

/**
 * Converts from CNY to the target currency.
 *
 * @param {string} symbol - The symbol of the target currency
 * @param {CurrenciesQuery} currencies - The currency data
 * @returns {number} - The conversion rate from CNY to the target currency
 */
export const conversionRateFromCNY = (
  symbol: string,
  currencies: CurrenciesQuery,
): number => {
  // If symbol or currencies are undefined, return 1.
  if (!symbol || !currencies) return 1

  // If the target currency is CNY, no conversion is needed.
  if (symbol === 'CNY') return 1

  // Get the conversion rate from CNY to USD.
  const rateCnyToUsd = currencies?.CNY?.results?.[0]?.usdtoc
  if (!rateCnyToUsd) return 1

  // Use conversionRateFromUSD to get the rate from USD to the target currency.
  const rateUsdToTarget = conversionRateFromUSD(symbol, currencies)
  if (!rateUsdToTarget) return 1

  // Calculate the conversion rate from CNY to the target currency.
  return rateCnyToUsd * rateUsdToTarget
}

export default conversionRateFromCNY
