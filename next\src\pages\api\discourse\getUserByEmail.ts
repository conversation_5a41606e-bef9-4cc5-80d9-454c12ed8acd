import type { NextApiRequest, NextApiResponse } from 'next'
import process from 'process'
import type { UserData } from '~/src/components/Auth/Types/UserData'
import { sanitizeEmail } from '~/src/features/auth/sanitize'
import { authHeaders } from '~/src/services/discourse/auth'

/**
 * Fetch a user by email from Discourse using the API
 *
 * @param email
 */
async function fetchUserByEmail(email: string) {
  // Construct the URL with query parameters
  const url = new URL(
    `${process.env.NEXT_PUBLIC_DISCOURSE_URL}/admin/users/list/new.json`,
  )
  url.searchParams.append('email', email)
  url.searchParams.append('show_emails', 'true')

  try {
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        ...authHeaders(),
      },
    })

    if (!response.ok) {
      // If the response is not OK, throw an error with the status
      throw new Error(`HTTP error! Status: ${response.status}`)
    }

    const data = await response.json()
    // Check if data contains any users and return the first one if available
    return data.length > 0 ? data[0] : null
  } catch (error) {
    console.error('Error fetching user by email #01:', error)
    return null
  }
}

/**
 * Handler to get a user by email
 *
 * @param req
 * @param res
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  try {
    if (!req.body?.email) {
      return res.status(400).json({ error: 'Email is required' })
    }

    const user = await fetchUserByEmail(sanitizeEmail(req.body.email))

    if (!user) {
      return res.status(200).json({})
    }

    // Prepare the user data to be returned
    const userData: UserData = {
      email: user.email,
      name: user.name || '',
      username: user.username || '',
    }

    return res.status(200).json(userData)
  } catch (error) {
    console.error('Error fetching user by email:', error)
    return res.status(500).json({ message: 'Server error' })
  }
}
