@use './../vars' as *;

.sectionTitle {
  margin: 0.875em 0;
  font-size: 1.5em;
  font-weight: 600;
  text-transform: capitalize;
}

.divider {
  width: 100%;
  height: 2px;
  background-color: $light-grey;
  margin: 1em 0;
}

.dividerBold {
  width: 100%;
  height: 1px;
  background-color: #000;
  margin: 1em 0;
}

.mdsIcon {
  display: block;
  width: 48px;
  height: 28px;
  fill: none;
  stroke: #000;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 2px;
  margin: 0 auto;

  &:last-of-type {
    margin-top: -20px;
  }
}

.iconDisable {
  stroke: #cccccc;
}

.newItem {
  border: thin solid #e2e1e1;
  border-bottom: 0;
  padding: 6px 15px;

  &:last-of-type {
    border-bottom: thin solid #e2e1e1;
  }

  a {
    width: 60%;
  }

  .itemSource {
    width: 22%;
    min-width: 150px;
  }

  p {
    width: 18%;
    min-width: 151px;
    text-align: right;
    white-space: nowrap;
  }
}

.digestWrapper {
  display: grid;
  grid-template-columns: 1fr 300px;
}
.chartWrapper {
  margin-bottom: 30px;
}
@media (max-width: 1280px) {
  .digestWrapper {
    grid-template-columns: auto;
    padding: 0 20px;
    font-size: 13px;
  }
  .chartLeft {
    display: flex;
    width: 100%;
    margin-bottom: 20px;
    gap: 20px;
  }
  .chartWrapper {
    width: calc(100% - 300px - 20px);
    margin-bottom: 0;
  }
}

@media (max-width: 767px) {
  .chartLeft {
    gap: 0;
    display: block;
  }
  .chartWrapper {
    width: 100%;
  }
}

@media (max-width: 575px) {
  .chartLeft {
    width: 100%;
  }

  .newItem {
    flex-direction: column;
    .itemSource {
      opacity: 0.7;
    }
    a,
    .itemSource,
    p {
      width: 100%;
      padding: 0;
      text-align: left;
    }
  }
}

.title {
  color: #000;
  cursor: pointer;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;

  &:hover {
    color: #c06a24;
  }
}
