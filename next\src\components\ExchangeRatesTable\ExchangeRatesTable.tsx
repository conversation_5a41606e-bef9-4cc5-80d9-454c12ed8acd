import dayjs from 'dayjs'
import Image from 'next/image'
import Link from 'next/link'
import { type FC, Suspense } from 'react'
import {
  Table as TableReponsive,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
} from 'react-super-responsive-table'
import 'react-super-responsive-table/dist/SuperResponsiveTableStyle.css'
import type { LiveSpotGoldRow } from '~/src/generated'
import { metals } from '~/src/lib/metals-factory.lib'
import colorize from '~/src/utils/colorize'
import cs from '~/src/utils/cs'
import { Timezones } from '~/src/utils/dates'
import { pf } from '~/src/utils/priceFormatter'
import { refetchInterval } from '~/src/utils/timestamps'
import { ErrBoundary } from '../ErrBoundary/ErrBoundary'
import { Query } from '../Query/Query'
import Table from '../Table/Table'
import styles from './ExchangeRatesTable.module.scss'

const ExchangeRatesTable: FC = () => {
  const fetcher = metals.exchangeRatesTable({
    options: {
      refetchInterval,
    },
  })
  return (
    <ErrBoundary>
      <Suspense fallback={<div>Loading...</div>}>
        <Query fetcher={fetcher}>
          {({ data }) => {
            return (
              <Table title="Exchange Rates" href="/price/forex">
                <TableReponsive className={cs([styles.styleTable, 'kitco-exchange-rates-table'])}>
                  <Thead className={styles.styleThead}>
                    <Tr>
                      <Th className="w-[19.2%] text-left font-normal">
                        Currencies
                      </Th>
                      <Th className="w-[11.8%] text-left font-normal">
                        NY Time
                      </Th>
                      <Th className="w-[11.5%] text-right font-normal">
                        X = 1 USD
                      </Th>
                      <Th className="w-[11.5%] text-right font-normal">
                        Change %
                      </Th>
                      <Th className="w-[11.5%] text-right font-normal">
                        X USD = 1
                      </Th>
                      <Th className="w-[11.5%] text-right font-normal">
                        Price / oz
                      </Th>
                      <Th className="w-[11.5%] text-right font-normal">
                        Change
                      </Th>
                      <Th className="w-[11.5%] text-right font-normal">
                        Change %
                      </Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {data?.GetLiveSpotGoldTableV3?.Table?.length &&
                      data?.GetLiveSpotGoldTableV3?.Table?.map(
                        (x: LiveSpotGoldRow) => {
                          return (
                            <Tr
                              key={x.Currency}
                              className={cs([styles.item, styles.loading])}
                            >
                              <Td>
                                <div className={styles.name}>
                                  <Image
                                    alt={`${x.Currency} Flag`}
                                    src={`/flags/${x.Currency}.png`}
                                    className={styles.flag}
                                    width={23}
                                    height={16}
                                    unoptimized={true}
                                  />
                                  <h3>
                                    <Link
                                      className="text-kitco-black hover:text-ktc-blue hover:underline transition-colors duration-200 ease-in-out"
                                      href={`/price/forex/USD${x.Currency}`}
                                    >
                                      {x.Currency}
                                    </Link>
                                  </h3>
                                </div>
                              </Td>
                              <Td>
                                <time
                                  dateTime={x?.Rate?.NYTime}
                                  className={styles.time}
                                >
                                  {x?.Rate?.NYTime
                                    ? dayjs
                                      .tz(x?.Rate?.NYTime, Timezones.NY)
                                      .format('MM/DD - HH:mm:ss')
                                    : '-'}
                                </time>
                              </Td>
                              <Td className="text-right">
                                <span>
                                  {x?.Currency === 'USD'
                                    ? '--'
                                    : x?.Rate.CurrencyToUsd?.toFixed(4)}
                                </span>
                              </Td>
                              <Td className="text-right">
                                <span
                                  className={cs([
                                    x?.Currency === 'USD'
                                      ? ''
                                      : colorize(x?.Rate.ChangePercent),
                                  ])}
                                >
                                  {x?.Currency === 'USD'
                                    ? '--'
                                    : `${x?.Rate?.ChangePercent?.toFixed(2)}%`}{' '}
                                </span>
                              </Td>
                              <Td className="text-right">
                                <span
                                  className={cs([styles.lastItemMidColumn])}
                                >
                                  {x?.Currency === 'USD'
                                    ? '--'
                                    : x?.Rate?.UsdToCurrency?.toFixed(4)}
                                </span>
                              </Td>
                              <Td className="text-right">
                                <span>{x?.Gold?.Price?.toFixed(2)}</span>
                              </Td>
                              <Td className="text-right">
                                <span
                                  className={cs([colorize(x?.Gold?.Change)])}
                                >
                                  {pf(x?.Gold?.Change)}
                                </span>
                              </Td>
                              <Td className="text-right">
                                <span
                                  className={cs([
                                    colorize(x?.Gold?.ChangePercent),
                                  ])}
                                  style={{ paddingRight: '.5em' }}
                                >
                                  {x?.Gold?.ChangePercent?.toFixed(2)}
                                  &#37;
                                </span>
                              </Td>
                            </Tr>
                          )
                        },
                      )}
                  </Tbody>
                </TableReponsive>
              </Table>
            )
          }}
        </Query>
      </Suspense>
    </ErrBoundary>
  )
}

export default ExchangeRatesTable
