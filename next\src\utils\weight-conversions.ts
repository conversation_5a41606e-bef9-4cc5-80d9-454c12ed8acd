const FRACTION_DIGITS = 3

function isEmptyNumber(value) {
  return value === 'NaN' || value === ''
}

function calculate(value) {
  const result = value.toFixed(FRACTION_DIGITS)
  if (isEmptyNumber(result)) {
    return '-'
  }
  return Number.parseFloat(result).toString()
}

export function renderNumberSafely(value) {
  if (isEmptyNumber(value)) {
    return '-'
  }
  return value
}

export function dwtToOunce(value) {
  return calculate(Number.parseFloat(value) / 20)
}

export function dwtToGram(value) {
  return calculate(Number.parseFloat(value) * 1.55517384)
}

export function ozToDwt(value) {
  return calculate(Number.parseFloat(value) * 20)
}

export function ozToGram(value) {
  return calculate(Number.parseFloat(value) * 31.1035)
}

export function gramToDwt(value) {
  return calculate(Number.parseFloat(value) / 1.55517384)
}

export function gramToOz(value) {
  return calculate(Number.parseFloat(value) / 31.1035)
}
