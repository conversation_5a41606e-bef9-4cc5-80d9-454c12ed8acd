interface FormatPercentageParams {
  value: number
  min?: number
  max?: number
  locales?: string
}

/**
 * Format the percentage
 *
 * @param {object} params - The parameters for formatting
 * @param {number} params.value - The value to format
 * @param {number} [params.min=0] - The minimum fraction digits
 * @param {number} [params.max=2] - The maximum fraction digits
 * @param {string} [params.locales='en-US'] - The locales to use
 * @returns {string} The formatted percentage
 */
export function formatPercentage({
  value,
  min = 2,
  max = 2,
  locales = 'en-US',
}: FormatPercentageParams): string {
  const options: Intl.NumberFormatOptions = {
    minimumFractionDigits: min,
    maximumFractionDigits: max,
  }

  // Convert value to positive (to avoid negative sign)
  value = Math.abs(value)

  // Get the formatted value to the correct locale
  return value.toLocaleString(locales, options)
}
