import { dehydrate, QueryClient } from '@tanstack/react-query'
import type { SortingState } from '@tanstack/table-core'
import type { GetServerSideProps, NextPage } from 'next'
import Head from 'next/head'
import { Suspense, useState } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import LatestNewsSidebar from '~/src/components/LatestNewsSidebar/LatestNewsSidebar'
import Layout from '~/src/components/Layout/Layout'
import MiningEquitiesDataTable from '~/src/components/MiningEquities/DataTable/MiningEquitiesDataTable'
import MiningEquitiesHeader from '~/src/components/MiningEquities/MiningEquitiesHeader'
import { PressReleaseSidebar } from '~/src/components/PressReleases/PressReleaseSidebar'
import {
  useMiningEquitiesData,
  useMiningEquitiesDataSSR,
} from '~/src/hooks/MiningEquities/useMiningEquitiesData'
import { getQuery } from '~/src/hooks/News/useLatestNewsSidebar'
import { MiningEquitiesQueries } from '~/src/lib/MiningEquities/Queries'
import { pressReleases } from '~/src/lib/PressReleases/Queries'
import type SortOption from '~/src/types/DataTable/SortOption'

const MiningEquities: NextPage = () => {
  // Get the data for the table
  const data = useMiningEquitiesData()

  // Store the sorting state
  const [sortBy, setSortBy] = useState<SortingState>([])

  const sortOptions: SortOption[] = [
    { label: 'Company', value: 'Name' },
    { label: 'Symbol', value: 'TVSymbol' },
    { label: 'Price', value: 'PriceVal' },
    { label: 'Change', value: 'ChangePercentageVal' },
    { label: 'Volume', value: 'VolumeVal' },
    { label: 'High', value: 'HighVal' },
    { label: 'Low', value: 'LowVal' },
  ]

  const handleSortApply = (value: SortingState) => {
    setSortBy(value)
  }

  const isLoading = !data

  return (
    <Layout title="Top Mining Equities: Investment Opportunities in the Mining Sector | KITCO">
      <Suspense fallback={<div>Loading...</div>}>
        <Head>
          <meta
            name="description"
            content={
              'Explore leading mining equities with KITCO and discover investment opportunities in the mining sector. Stay informed with the latest market trends and company performances.'
            }
          />
          <meta
            name="keywords"
            content="mining equities, mining stocks, investment in mining, mining sector analysis, mining trends, mining companies, mining investments"
          />
          <meta name="author" content="KITCO Metals Inc." />
          <meta
            property="og:title"
            content="Top Mining Equities: Investment Opportunities in the Mining Sector | KITCO"
          />
          <meta
            property="og:description"
            content="Explore leading mining equities with KITCO and discover investment opportunities in the mining sector. Stay informed with the latest market trends and company performances."
          />
          <meta
            property="og:image"
            content={
              process.env.NEXT_PUBLIC_URL + '/open-graph/kitco-mining-og.png'
            }
          />
          <meta property="og:image:width" content="1200" />
          <meta property="og:image:height" content="630" />
          <meta
            property="og:url"
            content={process.env.NEXT_PUBLIC_URL + '/mining/mining-equities'}
          />
          <meta property="og:type" content={process.env.NEXT_PUBLIC_URL} />
          <meta name="twitter:card" content="summary_large_image" />
          <meta
            name="twitter:title"
            content="Top Mining Equities: Investment Opportunities in the Mining Sector | KITCO"
          />
          <meta
            name="twitter:description"
            content="Explore leading mining equities with KITCO and discover investment opportunities in the mining sector. Stay informed with the latest market trends and company performances."
          />
          <meta
            name="twitter:image"
            content={
              process.env.NEXT_PUBLIC_URL + '/open-graph/kitco-mining-og.png'
            }
          />
          <link
            rel="canonical"
            href={process.env.NEXT_PUBLIC_URL + '/mining/mining-equities'}
          />

          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify({
                '@context': 'https://schema.org',
                '@type': 'WebPage',
                name: 'Top Mining Equities: Investment Opportunities in the Mining Sector | KITCO',
                description:
                  'Explore leading mining equities with KITCO and discover investment opportunities in the mining sector. Stay informed with the latest market trends and company performances.',
                url: process.env.NEXT_PUBLIC_URL + '/mining/mining-equities',
                image:
                  process.env.NEXT_PUBLIC_URL +
                  '/open-graph/kitco-mining-og.png',
                publisher: {
                  '@type': 'Organization',
                  name: 'KITCO Metals Inc.',
                  logo: {
                    '@type': 'ImageObject',
                    url: process.env.NEXT_PUBLIC_URL + '/logos/kitco-logo.svg',
                  },
                },
                mainEntityOfPage: {
                  '@type': 'WebPage',
                  '@id':
                    process.env.NEXT_PUBLIC_URL + '/mining/mining-equities',
                },
              }),
            }}
          />

          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify({
                '@context': 'https://schema.org',
                '@type': 'BreadcrumbList',
                itemListElement: [
                  {
                    '@type': 'ListItem',
                    position: 1,
                    name: 'Home',
                    item: process.env.NEXT_PUBLIC_URL,
                  },
                  {
                    '@type': 'ListItem',
                    position: 2,
                    name: 'Mining Equities',
                    item:
                      process.env.NEXT_PUBLIC_URL + '/mining/mining-equities',
                  },
                ],
              }),
            }}
          />
        </Head>
        <div className="mx-auto grid max-w-full grid-cols-1 gap-8 md:p-2 lg:grid-cols-[1fr_1fr_1fr_336px]">
          <main className="flex flex-col items-center space-y-4 sm:space-y-10 lg:col-span-3">
            <div className="w-full max-w-full p-4 md:p-0 lg:w-full lg:max-w-full mt-2.5">
              <ErrBoundary errorTitle="Mining Equities Header Error">
                <MiningEquitiesHeader
                  sortOptions={sortOptions}
                  onSortApply={handleSortApply}
                />
              </ErrBoundary>
            </div>

            <div className="mt-10 w-full max-w-full p-0 md:rounded-xl md:p-4 md:shadow-[0_0_40px_0_rgba(18,18,18,0.08)] lg:w-full lg:max-w-full">
              <ErrBoundary errorTitle="Mining Equities DataTable Error">
                <MiningEquitiesDataTable
                  data={data}
                  isLoading={isLoading}
                  sortBy={sortBy}
                />
              </ErrBoundary>
            </div>
          </main>
          <aside className="flex flex-col items-center justify-start gap-6 overflow-clip">
            <AdvertisingSlot
              id={'right-rail-amq'}
              className="mx-auto h-[250px] w-[300px]
                  lg:h-[600px] lg:w-[336px] amqdAds:flex amqdAds:justify-center no-print"
            />

            <PressReleaseSidebar className="flex flex-col space-y-4 p-4 sm:max-w-[750px] md:p-0" />

            <AdvertisingSlot
              id={'right-rail-2'}
              className="mx-auto h-[250px] w-[300px] lg:h-[600px]"
            />

            <LatestNewsSidebar
              limit={5}
              category="mining"
              className="flex flex-col space-y-4 p-4 sm:max-w-[750px] md:p-0"
            />

            <AdvertisingSlot
              id={'right-rail-3'}
              className="sticky top-4 mx-auto h-[250px] w-[300px] lg:h-[600px]"
            />
          </aside>
        </div>
        <AdvertisingSlot
          id={'footer'}
          className="after:font-['Font Awesome 5 Pro]' fixed bottom-0 left-1/2
            z-20
            w-[320px]
            -translate-x-1/2 after:absolute after:right-0 after:top-[-25px] after:w-[20px] after:cursor-pointer after:rounded after:bg-[#373737] after:text-center after:text-lg after:font-bold after:leading-[1.1] after:text-white after:content-['X'] tablet:h-[90px] tablet:w-[728px] desktop:hidden"
        />
      </Suspense>
    </Layout>
  )
}

export const getServerSideProps: GetServerSideProps = async () => {
  const queryClient = new QueryClient()

  // Prefetch the queries
  await queryClient.prefetchQuery(MiningEquitiesQueries.miningEquitiesTable())
  await queryClient.prefetchQuery(
    pressReleases.nodeListPressReleasesQueue({
      variables: {
        limit: 6,
        offset: 0,
        queueId: 'press_releases',
      },
      options: {
        enabled: true,
      },
    }),
  )
  // Query for the latest news
  await queryClient.prefetchQuery(getQuery(5, 'mining'))

  // Dehydrate the query client
  const data = await useMiningEquitiesDataSSR(queryClient)

  return {
    props: {
      dehydratedState: JSON.parse(JSON.stringify(dehydrate(queryClient))),

      // We only load the first 10 items for the SSR
      // So the page loads faster and the user can see some data
      // while the rest of the data is being loaded
      ssrData: data.slice(0, 10),
    },
  }
}

export default MiningEquities
