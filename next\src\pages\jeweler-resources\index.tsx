import Image from 'next/image'
import type { ReactNode } from 'react'
import { JewelryResourcesTitle } from '~/src/components/JewelryTitle/JewelryTitle.component'
import LayoutJewelers from '~/src/components/LayoutJewelers/LayoutJewelers'
import cs from '~/src/utils/cs'
import styles from './jeweler-resources.module.scss'

const JewelerResources = () => {
  return (
    <LayoutJewelers title={'Jeweler Resources'}>
      <div className="mx-auto box-border w-full max-w-full px-5 md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <JewelryResourcesTitle />
      </div>
      <MainJewelerResources />
      <div className="mx-auto mb-[120px] box-border w-full max-w-full px-5 md:px-10 lg:px-10 xl:w-[1024px] xl:px-0">
        <ScrapItAndMetalynx />
      </div>
    </LayoutJewelers>
  )
}

export default JewelerResources

const MainJewelerResources = (): ReactNode => {
  return (
    <div className={styles.container}>
      <main className="bg-[#eeeeee] overflow-y-scroll lg:overflow-y-hidden scrollbar-hide">
        <iframe
          src={'https://scrapit.prod.kitco.com/'}
          style={{ width: '100%', height: '1400px' }}
          sandbox="allow-same-origin allow-scripts allow-popups"
        />
      </main>
    </div>
  )
}

const ScrapItAndMetalynx = (): ReactNode => {
  return (
    <>
      <div className="flex gap-6 pt-5 md:pt-10">
        <div className={cs(['w-7/12', styles.download])}>
          <div className="mb-[60px] mt-[15px] flex">
            <div className="mt-2 w-[50px] md:w-auto">
              <Image
                src="/jewelers/icons/scrapit.svg"
                alt="ScrapIt!"
                width={50}
                height={50}
                unoptimized={true}
              />
            </div>
            <div className="ml-[20px] w-[calc(100%_-_70px)] md:w-auto">
              <h1
                className="text-[15px] font-semibold text-[#f07802] md:text-[26px]"
                style={{ fontFamily: 'Arial, Verdana, san-serif' }}
              >
                Download our ScrapIt! app today
              </h1>
              <h2 className="text-[18px]">
                <strong>Calculate gold scrap value your way.</strong>
                <br />
                Real-time scrap gold calculator app for professionals.
              </h2>
              <p className="mt-2 flex">
                <a
                  href="https://itunes.apple.com/us/app/scrapit!/id921590083"
                  title="Download ScrapIt! on AppStore"
                  target="_blank"
                  rel="noreferrer"
                >
                  <Image
                    src="/jewelers/icons/app_store.svg"
                    alt="ScrapIt on AppStore"
                    className="h-[35px] w-[118px] pr-[13px]"
                    unoptimized={true}
                    width={118}
                    height={35}
                  />
                </a>
                <a
                  href="https://play.google.com/store/apps/details?id=com.kitco.scrapit&hl=en"
                  title="Download ScrapIt! on GooglePlay"
                  target="_blank"
                  rel="noreferrer"
                >
                  <Image
                    src="/jewelers/icons/ggplay_store.svg"
                    alt="Download ScrapIt! on GooglePlay"
                    className="h-[35px] w-[118px]"
                    unoptimized={true}
                    width={118}
                    height={35}
                  />
                </a>
              </p>
            </div>
          </div>
          <div className="flex">
            <div className="mt-2 w-[50px] md:w-auto">
              <Image
                src="/jewelers/icons/metalynx.svg"
                alt="Metalynx"
                unoptimized={true}
                width={50}
                height={50}
              />
            </div>
            <div className="ml-[20px] w-[calc(100%_-_70px)] md:w-[calc(100%_-_97px)]">
              <h1
                className="text-[15px] font-semibold text-[#f07802] md:text-[26px]"
                style={{ fontFamily: 'Arial, Verdana, san-serif' }}
              >
                Download Kitco&apos;s Metalynx app now
              </h1>
              <h2 className="text-[18px]">
                <strong>Plan. Measure. Create. </strong>
                <br />
                Metalynx, your jewelry-making planner app.
              </h2>
              <p className="mt-2 text-[14px]">
                Metalynx is a free handy app from Kitco Inc. that calculates the
                weight or length of an item after you supply the dimensions. It
                will work for round or square wire, tube, sheet, and half-round
                wire. Our versatile app can even give you the difference in
                weight of any product based on a change in the karat or shape of
                the product.{' '}
              </p>
              <p className="mt-2 text-[14px]">
                Metalynx supports all weight and measurement standards, allowing
                you to work with pennyweights or grams, inches or millimeters.
                Thicknesses can be entered in gauge or millimeters, and
                conversions are done instantly.
              </p>
              <p className="mt-2 flex">
                <a
                  href="https://itunes.apple.com/us/app/metalynx/id613591018?mt=8"
                  title="Download Kitco's Metalynx app on AppStore"
                  target="_blank"
                  rel="noreferrer"
                >
                  <Image
                    src="/jewelers/icons/app_store.svg"
                    alt="Metalynx on AppStore"
                    className="h-[35px] w-[118px] pr-[13px]"
                    unoptimized={true}
                    width={118}
                    height={35}
                  />
                </a>
                <a
                  href="https://play.google.com/store/apps/details?id=com.kitco.metalynx"
                  title="Download Metalynx app on GooglePlay"
                  target="_blank"
                  rel="noreferrer"
                >
                  <Image
                    src="/jewelers/icons/ggplay_store.svg"
                    alt="Download Metalynx on GooglePlay"
                    className="h-[35px] w-[118px]"
                    unoptimized={true}
                    width={118}
                    height={35}
                  />
                </a>
              </p>
            </div>
          </div>
        </div>
        <div className={cs(['w-5/12 self-center', styles.phone])}>
          <Image
            width={436}
            height={496}
            alt="ScrapIt!"
            src="/jewelers/devices/ScrapIt-Metalynx-mobile-apps.jpg"
            title="ScrapIt!"
            unoptimized={true}
          />
        </div>
      </div>
    </>
  )
}
