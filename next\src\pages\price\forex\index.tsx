import { AdvertisingSlot } from 'react-advertising'
import { AdditionalForexQuotesBlock } from '~/src/components/AdditionalForexQuotesBlock/AdditionalForexQuotesBlock'
import ForexPageTitle from '~/src/components/ForexPageTitle/ForexPageTitle'
import Layout from '~/src/components/Layout/Layout'
import {
  TradingViewCryptoQuotes,
  TradingViewForexChart,
  TradingViewForexMajorRates,
} from '~/src/components/trading-view-iframes'
import cs from '~/src/utils/cs'
import styles from './index.module.scss'

const Forex = () => {
  return (
    <Layout
      title={
        'Forex | Foreign Exchange | FX Markets | Currencies | Currency converter'
      }
    >
      <div className={styles.container}>
        <main>
          <section className={styles.section}>
            <ForexPageTitle
              title={'Forex Major Rates'}
              subtitle={'Forex Charts and Quotes by TradingView'}
            />
            <TradingViewForexMajorRates />
          </section>
          <section className={cs([styles.section, styles.twoColumn])}>
            <div>
              <ForexPageTitle title={'Additional Forex Quotes'} />
              <AdditionalForexQuotesBlock />
            </div>
            <div>
              <div>
                <ForexPageTitle title={'Forex Charts'} />
                <section className={styles.section}>
                  <div className={styles.block}>
                    <TradingViewForexChart symbol={'USDCAD'} />
                  </div>
                  <div className={styles.block}>
                    <TradingViewForexChart symbol={'USDEUR'} />
                  </div>
                  <div className={styles.block}>
                    <TradingViewForexChart symbol={'USDGBP'} />
                  </div>
                </section>
              </div>
              <div>
                <ForexPageTitle title={'Live Cryptocurrency Quotes'} />
                <section className={styles.section}>
                  <TradingViewCryptoQuotes />
                </section>
              </div>
            </div>
          </section>
        </main>
        <aside>
          <AdvertisingSlot
            id={'right-rail-1'}
            className="sticky top-[100px] mx-auto hidden h-[250px] w-[300px] desktop:block no-print"
          />
        </aside>
      </div>
    </Layout>
  )
}

export default Forex
