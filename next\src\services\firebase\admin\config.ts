import admin from 'firebase-admin'

const adminSecret = require('~/secrets/firebase-admin.json')

/**
 * Initialize the Firebase Admin SDK
 */
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(adminSecret),
    databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL,
  })
}

/**
 * Get the Realtime Database service for the default app
 */
export const getDatabase = () => {
  // Get a reference to the Firestore service using the default app
  return admin.database()
}

export default admin
