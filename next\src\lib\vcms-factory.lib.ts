import type { UseQueryOptions } from '@tanstack/react-query'
import { gql } from 'graphql-request'
import type {
  VideoConsumerCategoriesQuery,
  VideoConsumerCategoriesQueryVariables,
  VideoConsumerCategoryByIdQuery,
  VideoConsumerCategoryByIdQueryVariables,
  VideoConsumerFeedQuery,
  VideoConsumerFeedQueryVariables,
  VideoConsumerSnippetBySlugQuery,
  VideoConsumerSnippetBySlugQueryVariables,
  VideoConsumerVideoByIdQuery,
  VideoConsumerVideoByIdQueryVariables,
} from '~/src/generated'
import { graphs } from '../services/database/fetcher'
import type QueryArgs from '../types/QueryArgs'

const feed = (
  args?: QueryArgs<VideoConsumerFeedQueryVariables, VideoConsumerFeedQuery>,
): UseQueryOptions<VideoConsumerFeedQuery> => {
  return {
    ...args?.options,
    queryKey: ['vcmsFeed', args?.variables],
    queryFn: async () => {
      return await graphs.videosPricesFetch(
        gql`
          query VideoConsumerFeed($upNext: Boolean, $latest: Boolean) {
            VideoConsumerFeed(upNext: $upNext, latest: $latest) {
              upNext {
                id
                headline
                uuid
                thumbnailUuid
                description
                updatedAt
                publishedAt
                source
                guests {
                  id
                  name
                }
                videoId
                urlAlias
                categories {
                  id
                  name
                  urlAlias
                }
              }
              latest {
                id
                headline
                uuid
                thumbnailUuid
                description
                updatedAt
                publishedAt
                source
                guests {
                  id
                  name
                }
                videoId
                urlAlias
                categories {
                  id
                  name
                  urlAlias
                }
              }
            }
          }
        `,
        args?.variables,
      )
    },
  }
}

const snippetBySlug = (
  args?: QueryArgs<
    VideoConsumerSnippetBySlugQueryVariables,
    VideoConsumerSnippetBySlugQuery
  >,
): UseQueryOptions<VideoConsumerSnippetBySlugQuery> => {
  // @ts-ignore
  return {
    ...args?.options,
    queryKey: ['snippetBySlug', args?.variables],
    queryFn: async () => {
      return await graphs.videosPricesFetch(
        gql`
          query VideoConsumerSnippetBySlug($slug: String!) {
            VideoConsumerSnippetBySlug(slug: $slug) {
              id
              headline
              uuid
              thumbnailUuid
              description
              updatedAt
              publishedAt
              source
              guests {
                id
                name
              }
              videoId
              urlAlias
              categories {
                id
                name
                urlAlias
              }
            }
          }
        `,
        args?.variables,
      )
    },
  }
}

const videoById = (
  args?: QueryArgs<
    VideoConsumerVideoByIdQueryVariables,
    VideoConsumerVideoByIdQuery
  >,
): UseQueryOptions<VideoConsumerVideoByIdQuery> => {
  return {
    ...args?.options,
    queryKey: ['videoById', args?.variables],
    queryFn: async () => {
      return await graphs.videosPricesFetch(
        gql`
          query VideoConsumerVideoById($id: Int!) {
            VideoConsumerVideoById(id: $id) {
              id
              duration
              uuid
            }
          }
        `,
        args?.variables,
      )
    },
  }
}

const categories = (
  args?: QueryArgs<
    VideoConsumerCategoriesQueryVariables,
    VideoConsumerCategoriesQuery
  >,
): UseQueryOptions<VideoConsumerCategoriesQuery> => {
  return {
    ...args?.options,
    queryKey: ['vcmsCategories', args?.variables],
    queryFn: async () =>
      await graphs.videosPricesFetch(
        gql`
          query VideoConsumerCategories {
            VideoConsumerCategories {
              id
              name
              urlAlias
              position
            }
          }
        `,
        args?.variables,
      ),
  }
}

const categoryById = (
  args?: QueryArgs<
    VideoConsumerCategoryByIdQueryVariables,
    VideoConsumerCategoryByIdQuery
  >,
): UseQueryOptions<VideoConsumerCategoryByIdQuery> => {
  return {
    ...args?.options,
    queryKey: ['categoryById', args?.variables],
    queryFn: async () => {
      return await graphs.videosPricesFetch(
        gql`
          query VideoConsumerCategoryById($id: Int!) {
            VideoConsumerCategoryById(id: $id) {
              id
              name
              urlAlias
              position
              edges {
                snippets {
                  id
                  headline
                  uuid
                  thumbnailUuid
                  description
                  updatedAt
                  publishedAt
                  source
                  guests {
                    id
                    name
                  }
                  videoId
                  urlAlias
                  categories {
                    id
                    name
                    urlAlias
                  }
                }
              }
            }
          }
        `,
        args?.variables,
      )
    },
  }
}

export const vcms = {
  feed,
  videoById,
  categories,
  categoryById,
  snippetBySlug,
}
