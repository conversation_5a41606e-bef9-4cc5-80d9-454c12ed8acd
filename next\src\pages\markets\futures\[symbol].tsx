import type { FC } from 'react'
import useFuturesContracts from '~/src/components-markets/FuturesCell/contractsDataCell'
import LatestNewsCell from '~/src/components-news/LatestNewsCell/LatestNewsCell'
import FutureForexTable from '~/src/components/FutureForexTable/FutureForexTable'
import Layout from '~/src/components/Layout/Layout'
import type { Exchanges, FuturesCategory } from '~/src/types'

interface ServerProps {
  name: string
  exchange: Exchanges
  category: FuturesCategory
}

export async function getServerSideProps({ query }) {
  const params = query.symbol.split('_')
  return {
    props: {
      name: params[0],
      category: params[1] ?? '',
      exchange: params[2] ?? 'CME',
    },
  }
}

const FuturesContracts: FC<ServerProps> = ({ name, category, exchange }) => {
  const [contracts] = useFuturesContracts(category, exchange)

  return (
    <Layout title="title">
      <div className="grid gap-8 sm:grid-cols-1 lg:grid-cols-layout-2">
        <div>
          <h1 className="mb-8 text-2xl font-semibold">{name} Futures</h1>
          <div className="mb-8">
            <FutureForexTable data={contracts} title={'Metals'} />
          </div>
        </div>
        <div>
          <LatestNewsCell />
        </div>
      </div>
    </Layout>
  )
}

export default FuturesContracts
